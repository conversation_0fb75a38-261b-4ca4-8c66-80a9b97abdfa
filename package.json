{"name": "zoomcar.com", "version": "2.0.0", "description": "zoomcar.com responsive web", "keywords": ["zcui", "v<PERSON><PERSON><PERSON>"], "homepage": "http://www.zoomcar.com", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON>er <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Arpit Dixit <<EMAIL>>"], "license": "MIT", "config": {"src": "./src", "public": "./public"}, "scripts": {"test": "npm-run-all test:*", "clean": "rm -rf $npm_package_config_public/build/*", "build": "npm run clean && webpack --config webpack.dev.js", "dev": "webpack-dev-server --config webpack.dev.js", "start": "npm run dev", "prod": "npm run clean && webpack --config webpack.prod.js", "production": "./node_modules/webpack/bin/webpack.js --config webpack.prod.js"}, "repository": {"type": "git", "url": "**************:zoomcar/mobile-web.git"}, "bugs": {"url": "https://github.com/zoomcar/mobile-web/issues"}, "dependencies": {"@bugsnag/js": "^7.11.0", "@bugsnag/plugin-vue": "^7.11.0", "@zoomcarindia/vue-ui-kit": "1.0.9-beta.1", "@zoomcarindia/web-login": "4.0.0-beta.7", "@zoomcarindia/web-payments": "3.0.5-beta.5", "axios": "^0.21.4", "date-fns": "2.0.0-alpha.1", "dexie": "^2.0.0", "es6-promise": "^4.1.1", "fuse.js": "^6.6.2", "leaflet": "latest", "leaflet-geometryutil": "^0.10.2", "moment": "^2.29.4", "promise-polyfill": "^6.0.2", "qs": "^6.5.2", "randomstring": "^1.2.2", "regenerator-runtime": "^0.13.5", "smoothscroll-polyfill": "^0.3.6", "uuid": "^9.0.0", "vue": "^2.7.14", "vue-carousel": "^0.18.0", "vue-demi": "^0.13.1", "vue-head": "^2.0.12", "vue-i18n": "^8.26.1", "vue-loader": "^15.10.0", "vue-multiselect": "^2.1.6", "vue-router": "^2.7.0", "vue-style-loader": "^4.1.2", "vue-toast-notification": "^0.6.2", "vue2-leaflet": "^2.5.2", "vue2-leaflet-movingmarker": "^1.1.0", "vue2-leaflet-polylinedecorator": "^2.0.1", "vueperslides": "^2.15.2", "vuex": "^2.3.1", "web-vitals": "^3.5.0", "whatwg-fetch": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/preset-env": "^7.9.6", "@babel/preset-flow": "^7.9.0", "autoprefixer": "^7.1.2", "babel-eslint": "^7.2.3", "babel-loader": "^8.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-module-resolver": "^4.0.0", "compression-webpack-plugin": "^4.0.0", "core-js": "^3.6.5", "cross-env": "^5.0.5", "css-loader": "^3.5.3", "dotenv": "^10.0.0", "dotenv-webpack": "^1.5.4", "eslint": "^4.4.1", "eslint-plugin-flowtype": "^2.35.0", "exports-loader": "^0.7.0", "file-loader": "^6.0.0", "flow-bin": "^0.52.0", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^4.3.0", "imports-loader": "^0.8.0", "mini-css-extract-plugin": "^0.9.0", "node-sass": "^4.5.3", "npm-run-all": "^4.0.2", "postcss": "^6.0.9", "postcss-loader": "^3.0.0", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^1.8.5", "style-ext-html-webpack-plugin": "^4.1.2", "style-loader": "^1.2.1", "url-loader": "^4.1.1", "webpack": "^4.43.0", "webpack-bundle-analyzer": "^2.13.1", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.11.2", "webpack-merge": "^4.2.2"}, "zcui": {"variant": "vue-pwa"}, "browserslist": ["> 0.25%", "not ie <= 11"]}