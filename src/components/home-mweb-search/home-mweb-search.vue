<template>
    <div class="home-mweb-search">
        <!-- Header Section -->
        <header class="home-mweb-search__header">
            <div class="home-mweb-search__header--left">
                <button aria-label="Toggle Sidebar" class="home-mweb-search__button--hamburger"
                    @click="$emit('toggleSidebar')">
                    <i class="z-hamburger" aria-hidden="true"></i>
                </button>
                <a aria-label="Zoomcar Logo" href="/">
                    <img src="/img/zoomcar-logo-new.png" alt="Zoomcar Logo" class="home-mweb-search__image--logo" />
                </a>
            </div>
            <div class="home-mweb-search__header--right" @click="showCitySelectionPopup">
                <i class="z-location" aria-hidden="true"></i>
                <span class="home-mweb-search__text" aria-label="Selected City">
                    {{ city }}
                </span>
                <button class="home-mweb-search__button--icon" aria-label="Change City">
                    <i class="z-chevron_down" aria-hidden="true"></i>
                </button>
            </div>
        </header>

        <!-- Search Form Section -->
        <form class="home-mweb-search__form">
            <div class="home-mweb-search__search-type">
                <div
                :class="['home-mweb-search__search-type--button',
                                { active: index == activeProduct },]"
                :key="index"
                v-for="(product, index) in homeProducts"
                @click="handleProductClick(product, index)"
                >
                <div
                    :class="['home-mweb-search__search-type--button-heading',
                            { 'active-heading': index === activeProduct }]"
                >
                    {{ product.title }}
                    <div
                    class="home-mweb-search__search-type--new-tag"
                    v-if="product?.new_tag"
                    >
                    New
                    </div>
                </div>
                <div class="home-mweb-search__search-type--subtext">
                    {{ product.sub_title }}
                </div>
                </div>
            </div>
            <div class="home-mweb-search__main">
                <span class="home-mweb-search__text--pretitle">{{ preTitle }}</span>
                <h1
                  class="home-mweb-search__text--title"
                  :style="isLongTitle ? { fontSize: '14px'} : {}"
                >{{ title }}</h1>
                <!-- Location Input -->
                <div
                class="home-mweb-search__input-group"
                @click="showLocationSelectionPopup"
                aria-label="Change Selected location"
                >
                <i class="z-search" aria-hidden="true"></i>
                <input
                    type="text"
                    v-model="inputOrigin"
                    @input="handleLocationChange"
                    placeholder="Search for the car location"
                    aria-label="Selected location"
                    class="home-mweb-search__input"
                    readonly
                />
                <button
                    class="home-mweb-search__button--icon"
                    @click.stop.prevent="handleCurrentLocationClick"
                    aria-label="Select Current Location"
                >
                    <i class="z-my_location" aria-hidden="true"></i>
                </button>
                </div>

                <!-- Trip Dates Section -->
                <div
                class="home-mweb-search__dates-container"
                @click="showDateSelectionPopup"
                >
                <div class="home-mweb-search__date">
                    <label class="home-mweb-search__text--date-label"> Trip Starts </label>
                    <time class="home-mweb-search__text--date-value">
                    {{ starts | formatDate("D MMM'YY, hA") }}
                    </time>
                </div>
                <div class="home-mweb-search__date">
                    <label class="home-mweb-search__text--date-label"> Trip Ends </label>
                    <time class="home-mweb-search__text--date-value">
                    {{ ends | formatDate("D MMM'YY, hA") }}
                    </time>
                </div>
                </div>

                <!-- HD Section -->
                <div class="home-mweb-search__hd-container">
                <check-box :checked="false" @CheckboxClick="HDClicked" />
                <div class="home-mweb-search__text--hd">
                    Delivery & Pick-up, from anywhere
                </div>
                </div>

                <!-- Submit Button -->
                <button
                type="submit"
                class="home-mweb-search__button--submit"
                @click.prevent="handleGetCarClick"
                >
                Search Cars
                </button>
            </div>
        </form>
        <calendar-popup
            @update="handleDateUpdate"
            @submit="handleDateSubmit"
            @close="showCalendar = false"
            :showCalendar="showCalendar"
        />
        <subscription-popup
                :showPopup="showSubscriptionPopup"
                @close="showSubscriptionPopup = false"
                @ctaClick="switchToSubscription()"
        />
        <SlidePopUp :isCustomLayout="true" :showPopUp="showLocation || showCitySelection"
            :showTopBorder="false" @modalClosed="handleSlidePopupClose" class="home-mweb-search__popup">


            <!--Location Selection Popup-->
            <section v-if="showLocation" class="home-mweb-search__popup-content">
                <div class="home-mweb-search__input-group">
                    <input type="text" v-model="inputOrigin" @input="handleLocationChange" @focus="onFocus"
                        @blur="onBlur" placeholder="Search for the car location" aria-label="Search location"
                        class="home-mweb-search__input" />
                    <i class="z-search" aria-hidden="true"></i>
                </div>
                <button class="home-mweb-search__button--current-location" @click="handleCurrentLocationClick">
                    <i class="z-my_location"></i>
                    <p class="home-mweb-search__text--current-location">
                        {{
                        currentLocationFetching
                        ? "Fetching location..."
                        : "Current Location"
                        }}
                    </p>
                </button>
                <location-list :list="suggested.suggested_pickup_locations" :title="'Suggested Locations'"
                    @update="handleLocationSelect" v-if="!searchedPlaces.length" />
                <location-list :list="recentPlacesList" :title="'Recently searched locations'"
                    @update="handleLocationSelect" v-if="!searchedPlaces.length" />
                <location-list :list="searchedPlaces" @update="handleLocationSelect" :title="`Search Results`" />
            </section>

            <!--City Selection Popup-->
            <section v-if="countries && showCitySelection" class="home-mweb-search__popup-content">
                <span class="home-mweb-search__text--city-search">Select City</span>
                <div class="home-mweb-search__input-group">
                    <input type="text" v-model="citySearch" @input="filterCities" placeholder="Search "
                        aria-label="Search City" class="home-mweb-search__input" />
                    <i class="z-search" aria-hidden="true"></i>
                </div>
                <section class="home-mweb-search__city-results">
                    <button :class="{
                            'home-mweb-search__button--city-item': true,
                            'home-mweb-search__button--city-item-selected': city == cityOption.link_name
                        }" v-for="cityOption in filteredCities" :key="cityOption.id" @click="
                            handleCityClick(
                                country[0].name,
                                cityOption.link_name,
                                cityOption.name,
                                country[0].iso_alpha_3,
                                cityOption
                            )
                            ">
                        {{ cityOption.name || "bangalore" }}
                    </button>
                </section>
            </section>
        </SlidePopUp>
    </div>
</template>

<script>
    import "./home-mweb-search.scss"
    import HomeMwebSearch from './home-mweb-search'
    export default HomeMwebSearch
</script>