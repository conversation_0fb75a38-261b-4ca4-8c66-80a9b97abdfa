import { mapGetters } from "vuex";
import segment from "~/pages/home-rebrand/analytics/segment";

export default {
  name: "accordion-home",
  props: {
    list: {
      default: () => [],
      type: Array,
    },
    title: {
      default: "",
      type: String,
    },
    section: {
      default: "",
      type: String,
    },
    routeParams: {
      default: () => {},
      type: Object,
    },
  },
  data() {
    return {
      accordion: {
        expand: false,
        tab: 0,
      },
      icons: {
        faq: [
          "/img/coins.png",
          "/img/language_select.png",
          "/img/rupee.png",
          "/img/checklist.png",
          "/img/car.png",
        ],
      },
      observer: undefined,
    };
  },
  mounted(){
    this.visibilityObserver();
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    handleAccordionClick(tab) {
      this.$emit('segment', this.segment.onClickEvent(`faq_clicked_${tab}`));
      if (this.accordion.tab === tab) {
        // If the clicked tab is already open, close it
        this.accordion = {
          tab: 0, // No tab is open
          expand: false,
        };
      } else {
        // Otherwise, open the clicked tab and close others
        this.accordion = {
          tab: tab, // Set the clicked tab as the active one
          expand: true, // Indicate that the tab is expanded
        };
      }
    },
    visibilityObserver() {
      const options = {
        threshold: [1.0] 
      };
      // IntersectionObserver callback
      const callback = (entries) => {
        entries.forEach((entry) => {
         if (entry.intersectionRatio === 1.0) {
          this.$emit('segment', this.segment.onClickEvent(`home_faq_section_visible`));
          }
        });
      };
      this.observer = new IntersectionObserver(callback, options);
      const target = document.getElementById(`home-faq`);
      this.observer.observe(target);
    },
  },
  computed: {
    ...mapGetters({
      city: "User/userCityLinkName",
    }),
    isFaqSection() {
      return this.section === "faq";
    },
    segment() {
      return segment.call(this);
    },
  },
};
