<template>
  <section class="footer-new">
    <section class="footer-new-info">
      <section class="footer-new-info-sections">
        <template v-for="(section, index) in sections">
          <div
            v-if="!hideAboutUs || section !== 'about us'"
            :key="section"
            :class="{
              'footer-new-info-sections-section': true,
              'footer-new-info-sections-section-selected':
                selectedInfoSection === section,
            }"
            @click="handleInfoSectionSelect(index)"
          >
            <div class="footer-new-info-sections-section-title">{{ section }}</div>
          </div>
        </template>
      </section>
      <section
        class="footer-new-info-description"
        v-if="selectedInfoSection === 'about us' && !hideAboutUs"
      >
        <div class="footer-new-info-description-block">
          <p class="footer-new-info-description-block-desc">
            Founded in 2013 and headquartered in Bengaluru, India, Zoomcar is a leading marketplace for car
            sharing focused in India. The Zoomcar community connects Hosts with Guests, who choose from a
            selection of cars for use at affordable prices, promoting sustainable, smart transportation solutions
            in India.  Zoomcar was listed on OTCQX in 2025.
          </p>
        </div>
      </section>
      <section
        class="footer-new-info-description"
        v-if="selectedInfoSection === 'about us' && airportFlow"
      >
        <div
          class="footer-new-info-description-block"
          v-for="airportDescriptionBlock in infoSectionAirportDescription"
          :key="airportDescriptionBlock.header"
        >
          <div class="footer-new-info-description-block-header">
            {{ airportDescriptionBlock.header | replacePlaceholders(routeParams) }}
          </div>
          <section
            v-for="desc in airportDescriptionBlock.data"
            :key="desc.description"
          >
            <div
              class="footer-new-info-description-block-sub-header"
              v-html="$options.filters.replacePlaceholders(desc.title, routeParams)"
            />

            <p
              class="footer-new-info-description-block-desc"
              v-html="$options.filters.replacePlaceholders(desc.description, routeParams)"
            />
          </section>
        </div>
      </section>
      <section
        class="footer-new-info-description"
        v-if="selectedInfoSection === 'about us' && !airportFlow"
      >
        <div
          class="footer-new-info-description-block"
          v-for="cityDescriptionBlock in infoSectionCityDescription"
          :key="cityDescriptionBlock.header"
        >
          <div class="footer-new-info-description-block-header">
            {{ cityDescriptionBlock.header | replacePlaceholders(routeParams)}}
          </div>
          <section
            v-for="desc in cityDescriptionBlock.data"
            :key="desc.description"
          >
            <div
              class="footer-new-info-description-block-sub-header"
              v-html="$options.filters.replacePlaceholders(desc.title, routeParams)"
            />

            <p
              class="footer-new-info-description-block-desc"
              v-html="$options.filters.replacePlaceholders(desc.description, routeParams)"
            />
          </section>
        </div>
      </section>
      <section class="footer-new-info-description">
        <div
          class="footer-new-info-description-block"
          v-for="descriptionBlock in infoSectionDescription"
          :key="descriptionBlock.header"
        >
          <div class="footer-new-info-description-block-header">
            {{ descriptionBlock.header | replacePlaceholders(routeParams) }}
            <a
              v-if="descriptionBlock.link"
              :href="descriptionBlock.link"
              class="footer-new-info-description-block-header-link"
              target="__blank"
              >here
            </a>
          </div>

          <div
            class="footer-new-info-description-block-header"
            v-if="smartbuySession && selectedInfoSection === 'help & support'"
          >
            Read more about Smartbuy terms and conditions
            <a
              v-if="descriptionBlock.link"
              href="https://www.zoomcar.com/smartbuy-tnc"
              class="footer-new-info-description-block-header-link"
              target="__blank"
              >here
            </a>
          </div>

          <div
            class="footer-new-info-description-block-header"
            v-if="airindia && selectedInfoSection === 'help & support'"
          >
            Read more about Air India terms and conditions
            <a
              v-if="descriptionBlock.link"
              href="https://www.zoomcar.com/airindia-tnc"
              class="footer-new-info-description-block-header-link"
              target="__blank"
              >here
            </a>
          </div>
          <div
            class="footer-new-info-description-block-header"
            v-if="airindiaexpress && selectedInfoSection === 'help & support'"
          >
            Read more about Air India Express terms and conditions
            <a
              v-if="descriptionBlock.link"
              href="https://www.zoomcar.com/airindiaexpress-tnc"
              class="footer-new-info-description-block-header-link"
              target="__blank"
              >here
            </a>
          </div>

          <section
            v-for="desc in descriptionBlock.data"
            :key="desc.description"
          >
            <div
              class="footer-new-info-description-block-sub-header"
              v-if="isDisplay(desc.title)"
            >
              {{ desc.title | replacePlaceholders(routeParams) }}
            </div>
            <p class="footer-new-info-description-block-desc">
              {{ desc.description | replacePlaceholders(routeParams) }}
            </p>
          </section>
        </div>
      </section>
    </section>
    <section class="footer-new-location">
      <div class="footer-new-location-countries">
        <div class="footer-new-location-countries-india">
          <div class="footer-new-location-countries-india-title">
            Car Rental Services in India
          </div>
          <div class="footer-new-location-countries-india-cities">
            <div
              class="footer-new-location-countries-india-cities-city"
              v-for="city in cityList"
              :key="city.name"
            >
              <a
                :href="`https://www.zoomcar.com/in/${city.link_name}`"
                class="footer-new-location-countries-india-cities-city-link"
              >
                {{ "Self Drive Cars in " + city.name }}</a
              >
            </div>
          </div>
          <div
            class="footer-new-location-countries-india-title footer-new-location-countries-india-airport"
          >
            Upcoming Car Rental at Airports in India
          </div>
          <div class="footer-new-location-countries-india-cities">
            <div
              class="footer-new-location-countries-india-cities-city"
              v-for="city in airports"
              :key="city"
            >
              <a
                :href="`https://www.zoomcar.com/in/${city}-airport-car-rental`"
                class="footer-new-location-countries-india-cities-city-link"
              >
                {{ "Self Drive Cars in " + city + " airport" }}</a
              >
            </div>
          </div>
        </div>
      </div>
      <section class="footer-new-location-social">
        <section class="footer-new-location-social-title">
          Let's keep in touch
        </section>
        <section class="footer-new-location-social-icons">
          <i
            class="social-icons twitter"
            alt="twitter"
            @click="handleSocialMediaIconClick('twitter')"
          />
          <i
            class="social-icons instagram"
            alt="instagram"
            @click="handleSocialMediaIconClick('instagram')"
          />
          <i
            class="social-icons youtube"
            alt="youtube"
            @click="handleSocialMediaIconClick('youtube')"
          />
          <i
            class="social-icons facebook"
            alt="facebook"
            @click="handleSocialMediaIconClick('facebook')"
          />
          <i
            class="social-icons linkedin"
            alt="linkedin"
            @click="handleSocialMediaIconClick('linkedin')"
          />
        </section>
      </section>
    </section>
    <section v-if="!micrositeFlow" class="footer-new-mobile-qr">
      <div class="footer-new-mobile-qr-title">DOWNLOAD ZOOMCAR APP</div>
      <div class="footer-new-mobile-qr-sub-title">
        {{ $t("footer_download_app_description") }}
      </div>
    </section>
    <section class="footer-new-logo-qr">
      <section class="footer-new-logo-qr-logo">
        <p class="footer-new-logo-qr-logo-text">Never</p>
        <p class="footer-new-logo-qr-logo-text">Stop</p>
        <p class="footer-new-logo-qr-logo-text">Living.</p>
      </section>
      <section v-if="!micrositeFlow" class="footer-new-logo-qr-qr">
        <section class="footer-new-logo-qr-qr-block">
          <div class="footer-new-logo-qr-qr-block-image">
            <img src="/img/image.png" alt="qr-code" />
          </div>
          <div class="footer-new-logo-qr-qr-block-text">
            {{ $t("footer_qr_description") }}
          </div>
        </section>
        <section class="footer-new-logo-qr-qr-desc">
          <div class="footer-new-logo-qr-qr-desc-title">
            {{ $t("footer_download_app") }}
          </div>
          <div class="footer-new-logo-qr-qr-desc-description">
            {{ $t("footer_download_app_description") }}
          </div>
          <div class="footer-new-logo-qr-qr-desc-buttons">
            <i
              class="playstore-icons google-play"
              alt="google-play"
              @click="handleAppStoreClick('google')"
            />
            <i
              class="playstore-icons apple"
              alt="apple-logo"
              @click="handleAppStoreClick('apple')"
            />
          </div>
        </section>
      </section>
    </section>
    <section class="footer-new-copyright">
      <div class="footer-new-copyright-line"></div>
      <div class="footer-new-copyright-content">
        By continuing past this page, you agree to our Terms of Service, Cookie
        Policy, Privacy Policy and Content Policies. All trademarks are
        properties of their respective owners. 2012-2023 © Zoomcar™ Ltd. All
        rights reserved.
      </div>
    </section>
  </section>
</template>
<script>
import "./footer-new.scss";
import FooterNew from "./footer-new";
export default FooterNew;
</script>
