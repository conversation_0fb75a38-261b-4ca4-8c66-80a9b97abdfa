import { mapGetters } from "vuex";
import footer from "../../data/footer.json";
import footer_seo_city from "~/data/footer_seo_city.json";
import footer_seo_airport from "~/data/footer_seo_airport.json";
import { airportPage, validCitiesForAirportPage } from "~/config/airport-config";
import { airindiaSession } from "~/helpers/microsite/airindia.js";
import { airindiaexpressSession } from "~/helpers/microsite/airindiaexpress.js";
import { microsite } from "~/helpers/microsite/utils";

export default {
  name: "footer-new",
  props: {
    hideAboutUs: {
      type: Boolean,
      default: false,
    },
    routeParams: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      info: {
        sections: [
          "about us",
          "company profile",
          /* "blogs",*/ "careers",
          "help & support",
        ],
        data: footer,
      },
      cityInfo: footer_seo_city,
      // selectedInfoSection: "about us",
      smartbuySession: false,
      airportInfo: footer_seo_airport,
      airports: validCitiesForAirportPage,
      currentSectionIndex: this.hideAboutUs ? 1 : 0,
    };
  },
  mounted() {
    this.smartbuySession = sessionStorage.getItem("referrer") === "smartbuy";
  },
  methods: {
    handleInfoSectionSelect(index) {
      this.currentSectionIndex = index;
    },
    handleSocialMediaIconClick(platform) {
      let url;
      switch (platform) {
        case "twitter":
          url = "https://twitter.com/zoomcar_india?lang=en";
          break;
        case "instagram":
          url = "https://www.instagram.com/zoomcar_india";
          break;
        case "youtube":
          url = "https://www.youtube.com/c/Zoomcar-Self-Drive-Car-Rental";
          break;
        case "facebook":
          url = "https://www.facebook.com/zoomcar.in";
          break;
        case "linkedin":
          url = "https://in.linkedin.com/company/zoomcar-india-pvt-ltd-";
          break;
      }
      window.open(url);
    },
    handleAppStoreClick(platform) {
      let url;
      switch (platform) {
        case "google":
          url = "https://play.google.com/store/apps/details?id=com.zoomcar";
          break;
        case "apple":
          url =
            "https://apps.apple.com/in/app/zoomcar-self-drive-car-rental/id889910218";
          break;
      }
      window.open(url);
    },
    isDisplay(title) {
      return title.trim().length;
    },
  },
  computed: {
    ...mapGetters({
      cities: "Location/cities",
      locale: "App/locale",
      country: "App/country",
      userCityLinkName: "User/userCityLinkName",
    }),
    cityList() {
      return this.cities("india");
    },
    infoSectionDescription() {
      if (this.country === "india") {
        if (this.locale === "en") {
          this.info.data = footer;
        }
      }
      return this.info.data[this.selectedInfoSection];
    },
    airportFlow() {
      const { airport_flow } = airportPage();
      return airport_flow;
    },
    infoSectionAirportDescription() {
      return this.airportInfo[this.userCityLinkName];
    },
    infoSectionCityDescription() {
      if (this.country === "india") {
        return this.cityInfo[this.userCityLinkName];
      } else return [];
    },
    sections() {
      let list = [
        "about us",
        "company profile",
        /* "blogs", */ "careers",
        "help & support",
      ];
      if (this.locale === "ar") {
        list = [
          "من نحن",
          "ملف الشركة",
          /* "المدونات", "فرص العمل",*/ "المساعدة والدعم",
        ];
      } else if (this.locale === "vi") {
        list = [
          "về chúng tôi",
          "Hồ sơ công ty",
          /* "blog", */ "sự nghiệp",
          "giúp đỡ & hỗ trợ",
        ];
      } else if (this.locale === "id") {
        list = [
          "tentang kami",
          "profil perusahaan",
          /* "blog", */ "karir",
          "bantuan & dukungan",
        ];
      } else if (this.locale === "en" && this.country == "india") {
        list = [
          "about us",
          "company profile",
          "careers",
          "help & support",
          "blogs",
        ];
      }
      return list;
    },
    airindia() {
      return airindiaSession();
    },
    airindiaexpress() {
      return airindiaexpressSession();
    },
    selectedInfoSection() {
      return this.sections[this.currentSectionIndex];
    },
    micrositeFlow() {
      return microsite();
    },
  },
};
