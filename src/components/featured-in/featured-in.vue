<template>
  <section class="featured-in">
    <TitleWithNeedle text="Featured In"/>
        <p class="featured-in__subtext">
          Trusted by leading publications, industry voices, and financial news outlets.
        </p>

      <div class="featured-in__agencies">
        <div
          v-for="(agency, index) in agencies"
          :key="index"
          class="featured-in__agency"
        >
          <a
            v-if="agency.linkUrl"
            :href="agency.linkUrl"
            target="_blank"
            rel="nofollow noopener noreferrer"
            class="featured-in__logo-link"
          >
            <img
              :src="agency.logoUrl"
              :alt="'Featured in ' + agency.name"
              loading="lazy"
              class="featured-in__logo"
            >
          </a>
        </div>
      </div>
  </section>
</template>

<script>
export default {
  name: 'FeaturedInSection',
  components: {
    TitleWithNeedle: () => import("~/components/title-with-needle"),
  },
  data() {
    return {
      agencies: [
        {
          name: 'Globe Newswire',
          logoUrl: 'https://www.globenewswire.com/content/logo/color.svg',
          linkUrl: 'https://www.globenewswire.com/'
        },
        {
          name: 'TechBullion',
          // logoUrl: 'https://www.zoomcar.com/img/techbullion.png',
          logoUrl: '/img/techbullion.png',
          linkUrl: 'https://techbullion.com/'
        },
        {
          name: 'CityBiz',
          logoUrl: 'https://www.citybiz.co/wp-content/uploads/2022/03/cbl-logo.png',
          linkUrl: 'https://www.citybiz.co/'
        },
        {
          name: 'Access Newswire',
          logoUrl: 'https://lirp.cdn-website.com/b585153d/dms3rep/multi/opt/1200-630-1920w.webp',
          linkUrl: 'https://accessnewswire.com/'
        },
        {
          name: 'ET Now',
          logoUrl: 'https://www.etnownews.com/assets/icons/svg/logo-large.svg',
          linkUrl: 'https://www.etnownews.com/'
        },
        {
          name: 'PR Newswire',
          logoUrl: 'https://www.prnewswire.com/content/dam/prnewswire/homepage/prn_cision_logo_desktop.png',
          linkUrl: 'https://www.prnewswire.com/'
        },
        {
          name: 'CNBC',
          logoUrl: 'https://sc.cnbcfm.com/applications/cnbc.com/staticcontent/img/cnbc_logo_new.png?v=1524171804&w=1920&h=1080',
          linkUrl: 'https://www.cnbc.com/'
        },
        {
          name: 'Financial Express',
          logoUrl: 'https://www.financialexpress.com/wp-content/uploads/2021/11/fe-og.png',
          linkUrl: 'https://www.financialexpress.com/'
        },
        {
          name: 'Business Wire',
          logoUrl: 'https://d1k42caodqw8lr.cloudfront.net/home_og_12e5f290cb.png',
          linkUrl: 'https://www.businesswire.com/'
        },
        {
          name: 'Tech in Asia',
          logoUrl: 'https://static.techinasia.com/assets/tia-ogimage.png',
          linkUrl: 'https://www.techinasia.com/'
        },
        {
          name: 'ET (The Economic Times)',
          logoUrl: 'https://economictimes.indiatimes.com/photo/65498029.cms',
          linkUrl: 'https://economictimes.indiatimes.com/'
        },
        {
          name: 'NDTV Profit',
          logoUrl: 'https://images.assettype.com/bloombergquint/2023-12/9bdeecba-8a48-49a6-9bd0-ec12d3284e5b/noimageplaceholder.png',
          linkUrl: 'https://www.ndtvprofit.com/'
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.featured-in {

  &__subtext {
    @include subhead;
    color: $label-color-secondary;
    text-align: center;
  }

  &__agencies {
    width: 100%;
    padding: $padding24;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: $padding04;
  }

  &__agency {
    height: auto;
  }

  &__logo {
    width: 200px;
    height: 96px;
    object-fit: contain;
    transition: all 0.3s ease-in-out;
    @media screen and (max-width: 768px) {
      width: 100px;
      height: 72px;
    }
  }

  &__logo-link {
    &:hover .featured-in__logo {
      filter: grayscale(0%);
    }
  }
}
</style>
