<template>
  <div class="subscription-popup">
    <div class="subscription-popup__desktop-popup" v-if="showPopup && !isMobile">
      <div
        class="subscription-popup__desktop-popup--close-btn"
        @click="$emit('close')"
      >
        <i class="z-close"></i>
      </div>
      <div class="subscription-popup__container" v-if="showPopup">
        <img
          class="subscription-popup__img"
          src="/img/subscription/subscription-popup-banner.png"
          alt="zoomcar subscription"
        />
        <div class="subscription-popup__info">
          <div class="subscription-popup__title">
            Benefits
          </div>
          <div
            :key="idx"
            class="subscription-popup__content"
            v-for="(benefit, idx) in subscription_data"
          >
          <div class="subscription-popup__content-img">
            <img
              :src="benefit.image_url"
            />
          </div>

            <div class="subscription-popup__content-text">
              <div class="subscription-popup__content-title">
                {{ benefit.title }}
              </div>
              <div class="subscription-popup__content-desc">
                {{ benefit.description }}
              </div>
            </div>
          </div>
          <div
            class="subscription-popup__button"
            @click="$emit('ctaClick')"
          >
            Got it
          </div>
        </div>
      </div>
    </div>
    <SlidePopUp
      v-else-if="showPopup && isMobile"
      :isCustomLayout="true"
      :showPopUp="showPopup"
      :showTopBorder="false"
      @modalClosed="$emit('close')"
      class="subscription-popup__popup"
    >
    <div class="subscription-popup__container" v-if="showPopup">
        <img
          class="subscription-popup__img"
          src="/img/subscription/subscription-popup-banner.png"
          alt="zoomcar subscription"
        />
        <div class="subscription-popup__info">
          <div class="subscription-popup__title">
            Benefits
          </div>
          <div
            :key="idx"
            class="subscription-popup__content"
            v-for="(benefit, idx) in subscription_data"
          >
          <div class="subscription-popup__content-img">
            <img
              :src="benefit.image_url"
            />
          </div>

            <div class="subscription-popup__content-text">
              <div class="subscription-popup__content-title">
                {{ benefit.title }}
              </div>
              <div class="subscription-popup__content-desc">
                {{ benefit.description }}
              </div>
            </div>
          </div>
          <div
            class="subscription-popup__button"
            @click="$emit('ctaClick')"
          >
            Got it
          </div>
        </div>
      </div>
    </SlidePopUp>
  </div>
</template>
<script>
import "./subscription-popup.scss";
import SubscriptionPopup from "./subscription-popup";
export default SubscriptionPopup;
</script>
