import { mapGetters, mapState } from "vuex";
import segment from "./analytics/segment";
import { GlobalEventEmitter } from "~/helpers/globalevent";

export default {
  name: "search-top-nav",
  components: {
    SearchCar: () =>
      import(/* webpackChunkName: "search-car" */ "~/components/search-car"),
    SearchCarMweb: () =>
      import(
        /* webpackChunkName: "search-car-mweb" */ "~/components/search-car-mweb"
      ),
    SearchLocationCalendarNew: () =>
      import(
        /* webpackChunkName: "search-location-calendar-new" */ "~/components/search-location-calendar-new"
      ),
  },
  data() {
    return {
      showSmartbuyLogo: false,
      smartbuySession: false,
      sbiYonoSession: false,
      route: {},
      isExpanded: false,
      logoImgUrl: require("../../../public/img/Zoomcar-icon-greenbg.svg"),
    };
  },
  props: {
    hideHamburger: {
      type: Boolean,
      default: false,
    },
    showbecomeHost: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState("App", ["countryCode", "locale"]),
    ...mapGetters({ userCity: "User/userCity", isMobile: "App/isMobile" }),
    segment() {
      return segment.call(this);
    },

    linkCountryCode() {
      return this.countryCode;
    },

    isHeader() {
      return this.$store.state.App.isHeader;
    },

    isLoggedIn() {
      return this.$store.state.User.info.auth_token;
    },

    user() {
      return this.$store.state.User.info;
    },
    cityLink() {
      return this.userCity.link_name || this.$route.params.city_link || "bangalore";
    },
    countryCode() {
      return this.$store.state.App.countryCode;
    },
  },

  methods: {
    handleHostUrlClick() {
      this.$emit("segment", this.segment.zapSubscribeClick);
    },
    handleHamburgerClick() {
      GlobalEventEmitter.$emit("handleToggleSideBar");
    },
    handleClick() {
      this.$emit("segment", this.segment.onLoginSignUpClick);
      this.$emit("login", () => {
        this.$router.go(0);
      });
    },
    expandOption() {
      this.isExpanded = !this.isExpanded;
    },
    handleBackClick() {
      const previousScreen = sessionStorage.getItem("previous_screen");
      if (
        previousScreen == "SearchPage" ||
        !previousScreen ||
        previousScreen == "HomePage" ||
        previousScreen == "LandingPageNew" ||
        previousScreen == "CarDetailPage"
      ) {
        this.$router.go(-1);
      } else if (previousScreen == "AddressPicker") {
        this.$router.go(-3);
      } else {
        this.$router.push({
          name: "HomePage",
          params: {
            city_link: this.city,
          }
          
        });
      }
    },
  },
  created() {
    this.smartbuySession = sessionStorage.getItem("referrer") === "smartbuy";
    this.sbiYonoSession = sessionStorage.getItem("referrer") === "yonosbi";
    this.showSmartbuyLogo =
      this.$route.name === "PaymentPage" &&
      sessionStorage.getItem("referrer") === "smartbuy";
  },
};
