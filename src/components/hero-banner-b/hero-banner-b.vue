<template>
  <div class="hero-banner-b">
    <city-popup
      v-if="showCityPopup"
      @cityClick="handleCityPopup"
      @close="showCityPopup = false"
    />
    <calendar-popup
      @update="handleDateUpdate"
      @submit="handleDateSubmit"
      @close="showCalendar = false"
      :showCalendar="showCalendar"
    />
    <subscription-popup
        :showPopup="showSubscriptionPopup"
        @close="showSubscriptionPopup = false"
        @ctaClick="switchToSubscription()"
        />
    <div class="hero-banner-b__location-popup" v-if="showLocation">
      <div class="hero-banner-b__location-popup--background">
        <div
          class="hero-banner-b__location-popup--close-button"
          @click="showLocation = false"
        >
          <i class="z-close"></i>
        </div>
        <div class="hero-banner-b__location-popup--container">
          <div
            class="hero-banner-b__location"
            @click="
              () => {
                $emit('segment', segment.onClickEvent('change_location_click'));
              }
            "
          >
            <div class="hero-banner-b__location--input">
              <div class="hero-banner-b__location--input-container">
                <div
                  v-if="inputValue || inputOrigin"
                  class="hero-banner-b__location--input-label"
                >
                  Location
                </div>
                <div class="hero-banner-b__location--input-field">
                  <div
                    v-if="!inputValue && !inputOrigin"
                    class="hero-banner-b__location--input-field-icon"
                  >
                    <img src="/img/search-icon.svg" alt="search" />
                  </div>
                  <input
                    type="text"
                    v-model="inputOrigin"
                    @input="handleLocationChange"
                    @focus="onFocus"
                    @blur="onBlur"
                    placeholder="Search for the car location"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="hero-banner-b__location-popup--fetch-location">
            <div
              class="hero-banner-b__location-popup--fetch-location-container"
              @click="handleCurrentLocationClick"
            >
              <i class="icon icon-my-location"></i>
              <p class="hero-banner-b__location-popup--fetch-location-text">
                {{
                  currentLocationFetching
                    ? "Fetching location..."
                    : "Current Location"
                }}
              </p>
            </div>
          </div>
          <div class="hero-banner-b__location-popup--options">
            <div
              v-if="searchedPlaces && searchedPlaces.length > 0"
              class="hero-banner-b__location-popup--search-results"
            >
              <location-list
                :list="searchedPlaces"
                @update="handleLocationSelect"
                :title="`Search Results`"
              />
            </div>
            <div
              v-else
              class="hero-banner-b__location-popup--locations"
              :style="{
                gridTemplateColumns:
                  recentPlacesList && recentPlacesList.length > 0
                    ? 'repeat(2, 1fr)'
                    : 'repeat(1, 1fr)',
              }"
            >
              <location-list
                :list="suggested.suggested_pickup_locations"
                :title="'Suggested Locations'"
                @update="handleLocationSelect"
              />
              <location-list
                :list="recentPlacesList"
                :title="'Recently searched locations'"
                @update="handleLocationSelect"
              />
            </div>
          </div>
          <div class="hero-banner-b__location-popup--button">
            <button
              @click="
                () => {
                  showLocation = false;
                  showCalendar = true;
                }
              "
            >
              CONTINUE
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="hero-banner-b__main" id="observed-box">
      <div class="hero-banner-b__content">
        <div class="hero-banner-b__search">
          <div class="hero-banner-b__search-type">
            <div
              :class="[
                'hero-banner-b__search-type--button',
                { active: index == activeProduct },
              ]"
              :key="index"
              v-for="(product, index) in homeProducts"
              @click="handleProductClick(product, index)"
              
            >
            <div 
          :class="[
            'hero-banner-b__search-type--button-heading',
            { 'active-heading': index === activeProduct }
          ]"
        >
                {{ product.title }}
                <div class="hero-banner-b__search-type--new-tag" v-if="product?.new_tag">New</div>
              </div>
              <div class="hero-banner-b__search-type--subtext">
                {{ product.sub_title }}
              </div>
            </div>
          </div>
          <div class="hero-banner-b__search--container">
            <div class="hero-banner-b__heading">
              <span class="hero-banner-b__heading--sub-text">
                {{ preTitle }}
              </span>
              <h1 class="hero-banner-b__heading--text">
                  {{ title }}
                <span
                  class="hero-banner-b__heading--text-city"
                  @click="showCityPopup = true"
                >
                  {{ cityDisplay }} <i class="z-chevron_down"></i>
                </span>
              </h1>
            </div>

            <div class="hero-banner-b__search--bar">
              <div
                class="hero-banner-b__location"
                @click="
                  () => {
                    showLocation = true;
                    $emit(
                      'segment',
                      segment.onClickEvent('change_location_click')
                    );
                  }
                "
              >
                <div class="hero-banner-b__location--input">
                  <div class="hero-banner-b__location--input-container">
                    <div
                      v-if="inputValue || inputOrigin"
                      class="hero-banner-b__location--input-label"
                    >
                      Location
                    </div>
                    <div class="hero-banner-b__location--input-field">
                      <div
                        v-if="!inputValue && !inputOrigin"
                        class="hero-banner-b__location--input-field-icon"
                      >
                        <img src="/img/search-icon.svg" alt="search" />
                      </div>
                      <input
                        type="text"
                        v-model="inputOrigin"
                        placeholder="Search for the car location"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="hero-banner-b__time"
                @click="
                  () => {
                    showCalendar = true;
                    $emit('segment', segment.onClickEvent('change_date_click'));
                  }
                "
              >
                <div class="hero-banner-b__time--container">
                  <div class="hero-banner-b__time--container-label">
                    Trip Starts
                  </div>
                  <div class="hero-banner-b__time--container-input">
                    {{ starts | formatDate("D MMM'YY, hA") }}
                  </div>
                </div>

                <div class="hero-banner-b__time--container">
                  <div class="hero-banner-b__time--container-label">
                    Trip Ends
                  </div>
                  <div class="hero-banner-b__time--container-input">
                    {{ ends | formatDate("D MMM'YY, hA") }}
                  </div>
                </div>
              </div>

              <div class="hero-banner-b__hd">
                <check-box :checked="false" @CheckboxClick="HDClicked" />

                <div class="hero-banner-b__hd-text">
                  Delivery & Pick-up, from anywhere
                </div>
              </div>

              <div
                class="hero-banner-b__search-button"
                @click="handleGetCarClick"
              >
                SEARCH
              </div>
            </div>
          </div>
        </div>

        <div class="hero-banner-b__image-content">
          <img
            :key="index"
            v-for="(img, index) in bannerContent"
            :src="img"
            :class="{ display: currentIndex === index }"
            alt="currentIndex"
            loading="eager"
          />
        </div>
      </div>

      <div class="hero-banner-b__banner">
        <div class="visually-hidden">
          "DRIVE ANYTIME ANYWHERE. With no commitment, unlimited options and
          hassle-free booking, your road to adventure's just a Zoomcar away!",
          "31,000+ high-quality car options , Unlimited kms to drive and stop
          anywhere , 100% Trip protection
          for a safe, hassle-free drive , 24/7 customer support for dedicated
          assistance",
        </div>
        <div class="hero-banner-b__banner--carousel">
          <!-- Carousel Images -->
          <div
            class="hero-banner-b__banner--carousel-images"
            :style="{
              width: `${100 * bannerImages.length}vw`,
              transform:
                'translateX(' +
                -currentIndex * (100 / bannerImages.length) +
                '%)',
            }"
          >
            <div
              class="hero-banner-b__banner--image-container"
              :key="index"
              v-for="(image, index) in bannerImages"
            >
              <img
                :key="index"
                :src="image"
                :alt="'Image ' + (index + 1)"
                class="hero-banner-b__banner--carousel-image"
                loading="eager"
              />
            </div>
          </div>
        </div>

        <!-- Dots -->
        <div class="hero-banner-b__banner--carousel-dots">
          <span
            v-for="(image, index) in bannerImages"
            :key="index"
            class="hero-banner-b__banner--dot"
            :class="{ active: currentIndex === index }"
            @click="currentSlide(index)"
          ></span>
        </div>
      </div>
    </div>
    <div class="hero-banner-b__search-sticky" v-if="isSticky">
      <div
        class="hero-banner-b__search-sticky--city-selection"
        @click="
          () => {
            showCityPopup = true;
            $emit('segment', segment.onClickEvent('change_city_click'));
          }
        "
      >
        <div class="hero-banner-b__search-sticky--city-selection-button">
          <div class="hero-banner-b__search-sticky--city-selection-text">
            {{ city }}
          </div>
          <i class="z-chevron_down"></i>
        </div>
      </div>

      <div
        class="hero-banner-b__search-sticky--location"
        @click="
          () => {
            showLocation = true;
            $emit('segment', segment.onClickEvent('change_location_click'));
          }
        "
      >
        <div class="hero-banner-b__search-sticky--location-input">
          <div class="hero-banner-b__search-sticky--location-input-container">
            <div
              v-if="inputValue || inputOrigin"
              class="hero-banner-b__search-sticky--location-input-label"
            >
              Location
            </div>
            <div class="hero-banner-b__search-sticky--location-input-field">
              <div
                v-if="!inputValue && !inputOrigin"
                class="hero-banner-b__search-sticky--location-input-icon"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M9.75 5C13.4779 5 16.5 8.02208 16.5 11.75C16.5 13.3436 15.9478 14.8082 15.0242 15.9629L20.7803 21.7197C21.0732 22.0126 21.0732 22.4874 20.7803 22.7803C20.51 23.0507 20.0845 23.0715 19.7903 22.8427L19.7197 22.7803L13.9629 17.0242C12.8082 17.9478 11.3436 18.5 9.75 18.5C6.02208 18.5 3 15.4779 3 11.75C3 8.02208 6.02208 5 9.75 5ZM9.75 6.5C6.85051 6.5 4.5 8.85051 4.5 11.75C4.5 14.6495 6.85051 17 9.75 17C12.6495 17 15 14.6495 15 11.75C15 8.85051 12.6495 6.5 9.75 6.5Z"
                    fill="#6D6D6D"
                  />
                </svg>
              </div>
              <input
                type="text"
                v-model="inputOrigin"
                @input="handleLocationChange"
                @focus="onFocus"
                @blur="onBlur"
                placeholder="Search for the car location"
              />
            </div>
          </div>
        </div>
      </div>

      <div
        class="hero-banner-b__search-sticky--time"
        @click="
          () => {
            showCalendar = true;
          }
        "
      >
        <div class="hero-banner-b__search-sticky--time-input">
          <div class="hero-banner-b__search-sticky--time-input-label">
            Trip Starts - Trip Ends
          </div>
          <div class="hero-banner-b__search-sticky--time-input-field">
            {{ starts | formatDate("D MMM'YY, hA") }} -
            {{ ends | formatDate("D MMM'YY, hA") }}
          </div>
        </div>
      </div>

      <button
        class="hero-banner-b__search-sticky--button"
        @click="handleGetCarClick"
      >
        SEARCH CAR
      </button>
    </div>
  </div>
</template>
<script>
import "./hero-banner-b.scss";
import HeroBannerB from "./hero-banner-b";
export default HeroBannerB;
</script>
