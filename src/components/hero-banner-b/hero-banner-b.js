import CheckBox from "~/components/check-box";
import segment from "~/pages/home-rebrand/analytics/segment";
import { InputText } from "@zoomcarindia/vue-ui-kit";
import CalendarPopup from "~/components/calendar-popup";
import CityPopup from "~/components/city-selection-popup";
import cookies from "~/helpers/cookies";
import { mapActions, mapGetters } from "vuex";
import { debounce, filterCitiesFromLatLng } from "~/helpers/common";
import geo from "~/helpers/geo";
import { v4 as uuidv4 } from "uuid";
import { getMatchingCity } from "~/helpers/city-match";
import SubscriptionPopup from "~/components/subscription-popup";

const db = window.zcDB;
const TABS = [
  {
    label: "Round Trip",
    icon: "z-swap",
    type: "round_trip",
  },
  {
    label: "Airport",
    icon: "z-airplane",
    type: "airport",
  },
];
export default {
  components: {
    CheckBox,
    InputText,
    CalendarPopup,
    SubscriptionPopup,
    CityPopup,
    CalendarV2: () =>
      import(/* webpackChunkName: "calendar-v2" */ "~/components/calendar-v2"),
    LocationList: () =>
      import(
        /* webpackChunkName: "location-list" */ "~/components/location-list"
      ),
  },

  props: {
    preTitle: {
      type: String,
      default: "Looking for Best Car Rentals?",
    },
    title: {
      type: String,
      default: "Book Self-Drive Cars in",
    },
  },

  data() {
    return {
      observer: null,
      isSticky: false,
      valueProps: [
        {
          text: "100% ",
          sub_text: "Hassle free Secured Trip ",
          icon: "/img/hompage-rebrand/shield_check.png",
        },
        {
          text: "25000+",
          sub_text: "Quality cars available",
          icon: "/img/hompage-rebrand/like_up.png",
        },
        {
          text: "Delivery",
          sub_text: "Anywhere, Anytime",
          icon: "/img/hompage-rebrand/location_drop.png",
        },
        {
          text: "Endless",
          sub_text: "Drives, pay by hour",
          icon: "/img/hompage-rebrand/time.png",
        },
      ],
      showCalendar: false,
      locationIcon: require("../../../public/img/location-icon-new.svg"),
      calenderIcon: require("../../../public/img/calender-new.svg"),
      showLocation: false,
      showCitySelection: false,
      selectedTab: "location",
      list: [],
      places: [],
      searchInput: "",
      recentPlaces: [],
      defaultLoc: {
        airport: null,
        round_trip: null,
      },
      currentLocationFetching: false,
      inputOrigin: "",
      dateStarts: undefined,
      dateEnds: undefined,
      inputValue: "",
      isFocused: false,
      isHDChecked: false,
      cityData: null,
      showCalendar: false,
      showCityPopup: false,
      bannerImages: [
        "/img/hero-banners/banner-bg-1.png",
        "/img/hero-banners/banner-bg-2.png",
        // "/img/hero-banners/subscription-bg.png",
      ],
      bannerContent: [
        "/img/hero-banners/banner-content-1.svg",
        "/img/hero-banners/banner-content-2-new.png",
        // "/img/hero-banners/subscription-content.svg",
      ],
      currentIndex: 0,
      homeProducts: [
        {
          title: "Daily Drives",
          sub_title: "Upto 7 days",
          type: "NORMAL",
        },
        {
          title: "Subscription",
          sub_title: "7 day+ rides",
          type: "ZOOMCAR_SUBSCRIPTION",
          new_tag: true,
          min_duration: 10080,
          enabled: true,
          default_start_time: 480,
        },
      ],
      activeProduct: 0,
      showSubscriptionPopup: false,
    };
  },
  created() {
    this.startAutoSlide();
    const city = this.$store.getters["App/city"];
    city &&
      db.history
        .where({ city })
        .sortBy("last_used")
        .then((history) => {
          this.allHistory =
            history.length > 5 ? history.slice(history.length - 5) : history; //showing last 5 searches in recent search

          this.recentPlaces = history.map((item) => {
            return {
              lat: item.coords.lat,
              lng: item.coords.lng,
              title: item.name,
            };
          });
        })
        .catch((err) => {
          console.error(err);
        });
  },
  mounted() {
    this.fetchSuggested();
    if (this.isSubscription) {
      this.activeProduct = 1;
    }
    this.createIntersectionObserver();
    this.dateStarts = this.starts;
    this.dateEnds = this.ends;
    let cityInfo = null;
    if (this.countries[0]?.cities) {
      cityInfo = this.countries[0]?.cities?.find(
        (city) => city.city_link_name === this.city
      );
      this.$store.commit("Search/updateOrigin", cityInfo);
    }

    this.homeProducts = this.homeProductsTabs;

    // Check if origin was recently set by locality coordinates
    const isLocalityOrigin = this.origin.setByLocality;
    const recentlySet =
      this.origin.lastSetTimestamp &&
      Date.now() - this.origin.lastSetTimestamp < 5000; // 5 seconds threshold

    if (
      (!this.origin.name ||
        (this.origin.name && !this.origin.lat) ||
        (this.origin.city !== this.$route.params.city_link && cityInfo)) &&
      (!isLocalityOrigin || !recentlySet)
    ) {
      this.inputOrigin = cityInfo?.home_ingress[0]?.location?.name;

      this.$store.commit("Search/setOrigin", {
        name: cityInfo?.home_ingress[0]?.location?.name,
        lat: cityInfo?.home_ingress[0]?.location?.lat,
        lng: cityInfo?.home_ingress[0]?.location?.lng,
      });

      this.setDefaultLocations();
      this.setAndUpdateOrigin(this.defaultLoc[this.product], cityInfo);
    } else this.inputOrigin = this.origin.name;
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  computed: {
    ...mapGetters({
      countries: "Location/countries",
      city: "App/city",
      origin: "Search/origin",
      product: "Search/product",
      minDate: "Search/minStarts",
      suggested: "Search/suggested",
      starts: "Search/starts",
      ends: "Search/ends",
      cities: "Location/cities",
      country: "App/country",
      countryCode: "App/countryCode",
      isCitiesLoaded: "Location/isCitiesLoaded",
      cityDetail: "Location/cityDetail",
      selectedCar: "HomeNew/selectedCar",
      userCityLinkName: "User/userCityLinkName",
      isSubscription: "Search/isSubscription",
      user: "User/user",
      homeProductsTabs: "Search/homeProductsTabs",
    }),

    segment() {
      return segment.call(this);
    },
    searchedPlaces() {
      return this.places;
    },
    recentPlacesList() {
      return this.recentPlaces;
    },
    countryData() {
      return this.countries?.[0] || {};
    },
    transformValue() {
      return this.currentIndex === 0
        ? "100%"
        : this.currentIndex === 1
        ? "0%"
        : "-100%";
    },
    cityDisplay() {
      return this.city
        ? `${this.city.charAt(0).toUpperCase()}${this.city.slice(1)}`
        : "City";
    },
  },
  methods: {
    ...mapActions({
      fetchSuggested: "Search/fetchSuggested",
      fetchSearched: "Search/fetchSearched",
      fetchPlaceDetails: "Search/fetchPlaceDetails",
      fetchGeoAddress: "Search/fetchGeoAddress",
      setProductType: "Search/setProductType",
      setSubscription: "Search/setSubscription",
    }),
    createIntersectionObserver() {
      const options = {
        threshold: [0, 0.1, 1],
      };

      // IntersectionObserver callback
      const callback = (entries) => {
        entries.forEach((entry) => {
          if (entry.intersectionRatio <= 0.1) {
            this.isSticky = true;
          } else {
            this.isSticky = false;
          }
        });
      };
      this.observer = new IntersectionObserver(callback, options);
      const target = this.$el.querySelector("#observed-box");
      this.observer.observe(target);
    },
    destroyIntersectionObserver() {
      if (this.observer) {
        this.observer.disconnect();
        this.observer = null;
      }
    },
    moveSlide(step) {
      this.currentIndex += step;
      if (this.currentIndex < 0) {
        this.currentIndex = this.bannerImages.length - 1;
      } else if (this.currentIndex >= this.bannerImages.length) {
        this.currentIndex = 0;
      }

      // this.$nextTick(() => {
      //   this.$el.querySelector(
      //     ".carousel-images"
      //   ).style.transform = `translateX(-${this.currentIndex * 100}%)`;
      // });
    },
    currentSlide(index) {
      this.stopAutoSlide();
      this.currentIndex = index;
      this.startAutoSlide();
    },
    startAutoSlide() {
      this.autoSlideInterval = setInterval(() => {
        this.moveSlide(1);
      }, 10000);
    },
    stopAutoSlide() {
      clearInterval(this.autoSlideInterval);
    },

    HDClicked() {
      this.isHDChecked = !this.isHDChecked;
      this.$emit("segment", this.segment.onClickEvent("hd_clicked"));
    },

    setDefaultLocations() {
      TABS.forEach((item) => {
        this.defaultLoc[item.type] = this.getselectedProductLocation(
          this.countries[0]?.cities,
          this.city,
          item.type
        );
      });
    },

    handleHover(val, state) {
      if (!val && state === "date") {
        this.dateStarts = this.starts;
        this.dateEnds = this.ends;
      }

      if (!val && state === "location") {
        this.inputOrigin = this.origin.name;
      }
    },

    handleDateSubmit(date) {
      this.$emit("segment", this.segment.onClickEvent("date_continue_click"));
      const startDate = new Date(date.start);
      const endDate = new Date(date.end);
      const diffInDays = (endDate - startDate) / (1000 * 60 * 60 * 24);

      this.$store.commit("Search/setDateTime", {
      point: "starts",
      date: date.start,
      });
      this.$store.commit("Search/setDateTime", {
      point: "ends",
      date: date.end,
      });
      this.showCalendar = false;
      if (!this.isSubscription && diffInDays >= 7) {
      this.showSubscriptionPopup = true;
      } else {
      this.handleGetCarClick();
      }
    },

    handleCityPopup(city) {
      this.showCityPopup = false;
      this.handleCityClick(
        this.country[0].name,
        city.link_name,
        city.name,
        this.country[0].iso_alpha_3,
        city
      );
    },

    handleCityClick(country, city_link, cityName, countryApiCode, cityData) {
      this.$emit("segment", this.segment.onClickEvent("city_selected"));
      this.cityData = cityData;
      this.showCitySelection = false;
      let countryCode = "in";
      this.$store.commit("Search/updateOrigin", cityData);

      cookies.create("city", cityName);
      if (cityData?.home_ingress?.length) {
        this.inputOrigin = cityData?.home_ingress[0]?.location?.address;
        this.$store.commit("Search/setOrigin", {
          name: cityData?.home_ingress[0]?.location?.name,
          lat: cityData?.home_ingress[0]?.location?.lat,
          lng: cityData?.home_ingress[0]?.location?.lng,
        });
      } else {
        this.fetchGeoAddress({
          coords: { lat: cityData?.lat, lng: cityData?.lng },
        }).then((res) => {
          //set the latitude, longitude and fetched adress name to Search/origin state
          if (res && res[0]) {
            this.inputOrigin = res[0]?.display_name;
            this.$store.commit("Search/setOrigin", {
              name: res[0]?.display_name,
              lat: cityData?.lat,
              lng: cityData?.lng,
            });
          }
        });
      }

      this.$store.commit("App/setCity", city_link);
      //this.$store.commit("App/setCountry", country);
      this.$store.commit("App/setCountryCode", countryCode);
      let cApiCode =
        countryApiCode == "undefined" || !countryApiCode
          ? "IND"
          : countryApiCode;
      this.$store.commit("App/setCountryApiCode", cApiCode);
      this.$store.commit("App/setIsCitySelected", true);
      //this.$store.commit("Location/setLandingPageFlow", true);
      this.$store.commit("App/setCity", city_link);

      //set city and origin
      this.$store.commit("Search/setOriginCity", city_link);
      this.$store.commit("User/setCityData", cityData);

      if (countryCode === "in") {
        this.$store.commit("App/setLocale", "en");
        this.$i18n.locale = "en";
        this.$store.dispatch("HomeNew/fetchWebSections", city_link);
        this.$store.dispatch("HomeNew/fetchCarCatalogue", city_link);
      }

      this.showCitySelection = false;
    },

    handleDateUpdate(date) {
      this.$emit("segment", this.segment.onClickEvent("date_updated_click"));
    },

    autocompleteApiCall: debounce(function (text) {
      if (!text || text.length < 4) {
        this.places = [];
        return;
      }
      this.fetchSearched({ text }).then((res) => {
        this.places = res || [];
      });
    }, 300),

    handleLocationChange(event) {
      const { value } = event.target;
      this.autocompleteApiCall(value);
    },

    handleLocationSelect(location) {
      this.$emit(
        "segment",
        this.segment.onClickEvent("location_selected_click")
      );

      if (!location.lng && !location.lat) {
        //location search flow
        this.fetchPlaceDetails({
          place_id: location.place_id,
        }).then((res) => {
          if (res) {
            if (res.zc_city !== this.userCityLinkName) {
              //setup website with data of changed city
              let selectedCity = {};
              if (this.cityDetail[res.zc_city]) {
                selectedCity = this.cityDetail[res.zc_city];
              } else {
                const matchingCity = getMatchingCity(
                  res,
                  this.countries[0]?.cities || []
                );
                if (matchingCity) {
                  selectedCity = {
                    ...res,
                    country_code: "IND",
                    countryCode: "in",
                    countryApiCode: "IND",
                    link_name: matchingCity.link_name,
                  };
                  res.zc_city = matchingCity.link_name;
                } else {
                  // Unserviceable
                  selectedCity = {
                    ...res,
                    country_code: "IND",
                    countryCode: "in",
                    countryApiCode: "IND",
                    link_name: this.userCityLinkName,
                  };
                  res.zc_city = this.userCityLinkName;
                }
              }

              this.$store.commit("App/setCountry", selectedCity.country);
              this.$store.commit(
                "App/setCountryCode",
                selectedCity.countryCode
              );
              this.$store.commit(
                "App/setCountryApiCode",
                selectedCity.countryApiCode
              );
              this.$store.commit("App/setCity", res.zc_city);
              this.$store.commit("User/setUserCityLinkName", res.zc_city);
              this.$store.commit("Search/setOriginCity", res.zc_city);
              this.$store.commit("User/setCityData", selectedCity);
              this.$store.commit("App/setIsCitySelected", true);
              this.$store.commit("App/setCityNotFound", false);
              this.$store.commit("HomeNew/setCarSelectionToSearchFlow", false);
              this.$store.commit("HomeNew/setAvailableDates", []);

              //it becomes a regular flow in case location changed in between marketplace car select to search
              this.$store.commit("HomeNew/setSelectedCar", {
                car_id: "",
                cargroup_id: "",
                search_params: {},
              });

              this.$router.replace({
                name: "HomePage",
                params: {
                  country_code: selectedCity.countryCode,
                  city_link: selectedCity.link_name,
                },
              });
            }
            this.$store.commit("Search/setOrigin", {
              name: location.title,
              lat: res.lat,
              lng: res.lng,
            });
            this.inputOrigin = location.title;
          }
        });
      } else {
        this.$store.commit("Search/setOrigin", {
          name: location.title,
          lat: location.lat,
          lng: location.lng,
        });
        this.inputOrigin = location.title;
      }

      this.showLocation = false;
      this.showCalendar = true;
    },

    handleGetCarClick() {
      this.$emit("segment", this.segment.onClickEvent("search_car_click"));

      //create new search session
      sessionStorage.setItem("search_session_id", uuidv4());
      sessionStorage.setItem("location", JSON.stringify(this.origin));

      this.$store.commit("HomeNew/setCarSelectionToSearchFlow", false);
      let query = {
        lat: this.origin.lat,
        lng: this.origin.lng,
        type: this.product,
        starts: this.starts?.getTime(),
        ends: this.ends?.getTime(),
        car_id: this.selectedCar.search_params["seek.car_id"],
        city_id: this.selectedCar.search_params["seek.city_id"],
        lat_e5: this.selectedCar.search_params["seek.lat_e5"],
        lng_e5: this.selectedCar.search_params["seek.lng_e5"],
        location_id: this.selectedCar.search_params["seek.location_id"],
        cargroup_id: this.selectedCar.cargroup_id,
        delivery_type: this.isHDChecked ? "home%2Cairport" : "",
        subscription: this.isSubscription,
      };
      this.$router.push({
        name: "SearchPage",
        query,
        params: {
          city_link: this.city || this.userCityLinkName  ,
          country_code: this.countryCode,
        },
      });
      sessionStorage.setItem("selectedCar", JSON.stringify(this.selectedCar));
    },

    handleCurrentLocationClick() {
      this.$emit(
        "segment",
        this.segment.onClickEvent("current_location_click")
      );

      let locationCoords = null;
      this.currentLocationFetching = true;
      geo
        .getCurrentPosition()
        .then((coords) => {
          locationCoords = coords;

          const validCities = filterCitiesFromLatLng(
            coords,
            this.countries[0]?.cities
          );

          let selectedCity;
          if (validCities && validCities.length) {
            selectedCity = validCities[0];
          }

          //fetch a list of nearby locations through geoservice geocode api by passing the coordinates
          this.fetchGeoAddress({
            coords: { lat: coords.latitude, lng: coords.longitude },
          }).then((res) => {
            //set the latitude, longitude and fetched adress name to Search/origin state
            if (res && res[0]) {
              this.inputOrigin = res[0]?.display_name;
              this.$store.commit("Search/setOrigin", {
                name: res[0]?.display_name,
                lat: locationCoords.latitude,
                lng: locationCoords.longitude,
              });

              if (this.userCityLinkName !== selectedCity.link_name) {
                this.$store.commit("App/setCountry", selectedCity.country);
                this.$store.commit(
                  "App/setCountryCode",
                  selectedCity.countryCode
                );
                this.$store.commit(
                  "App/setCountryApiCode",
                  selectedCity.countryApiCode
                );
                this.$store.commit("App/setCity", selectedCity.link_name);
                this.$store.commit(
                  "User/setUserCityLinkName",
                  selectedCity.link_name
                );
                this.$store.commit(
                  "Search/setOriginCity",
                  selectedCity.link_name
                );
                this.$store.commit("User/setCityData", selectedCity);
                this.$store.commit("App/setIsCitySelected", true);
                this.$store.commit("App/setCityNotFound", false);

                //different city
                this.$store.commit(
                  "HomeNew/setCarSelectionToSearchFlow",
                  false
                );
                this.$store.commit("HomeNew/setAvailableDates", []);

                //it becomes a regular flow in case location changed in between marketplace car select to search
                this.$store.commit("HomeNew/setSelectedCar", {
                  car_id: "",
                  cargroup_id: "",
                  search_params: {},
                });
                if (this.isSearchflow) {
                  const currRoute = JSON.parse(sessionStorage.getItem("route"));
                  if (currRoute.name === "SearchPage") {
                    const params = {
                      city_link: selectedCity.link_name,
                      country_code: selectedCity.countryCode,
                    };
                    const query = {
                      ...currRoute?.query,
                      lat: locationCoords.latitude,
                      lng: locationCoords.longitude,
                      delivery_type: this.isHDChecked ? "home%2Cairport" : "",
                    };
                    this.$router.push({
                      name: "SearchPage",
                      query: query,
                      params: params,
                    });
                  }
                } else {
                  this.$router.replace({
                    name: "HomePage",
                    params: {
                      country_code: selectedCity.countryCode,
                      city_link: selectedCity.link_name,
                    },
                  });
                }
              }
            }
          });
        })
        .finally(() => {
          this.currentLocationFetching = false;
        });
      this.showLocation = false;
    },

    getselectedProductLocation(cities, cityLinkName, product) {
      const cityInfo = cities?.find(
        (city) => city.city_link_name === cityLinkName
      );
      this.homeProducts = this.homeProductsTabs;
      const locationInfo = cityInfo?.home_ingress?.find(
        (loc) => loc?.type?.toLowerCase() === product?.toLowerCase()
      );
      if (product == "round_trip") {
        this.inputOrigin = locationInfo?.location.name;
        this.$store.commit("Search/setOrigin", {
          name: locationInfo?.location?.name,
          lat: locationInfo?.location?.lat,
          lng: locationInfo?.location?.lng,
        });
      }
      return locationInfo?.location;
    },

    getCityInfo() {
      const cityInfo = this.countries[0]?.cities?.find(
        (city) => city.city_link_name === this.userCityLinkName
      );
      return cityInfo;
    },

    setAndUpdateOrigin(loc, cityInfo) {
      this.$store.commit("Search/setOrigin", {
        name: loc?.name,
        lat: loc?.lat,
        lng: loc?.lng,
      });
      this.$store.commit("Search/updateOrigin", cityInfo);
      this.$store.commit("Search/resetEndDateTime");
      this.inputOrigin = this.origin.name;
    },

    handleMapClick() {
      this.$router.push({
        name: "HomePage",
        params: {
          city_link: this.userCityLinkName,
          country_code: this.countryCode,
        },
        query: {
          q: "address",
          loc: this.product,
          map: true,
          search: this.isSearchflow,
        },
      });
    },

    textTrim(text) {
      if (text && text.length > 30) {
        return text.slice(0, 30) + "...";
      } else return text;
    },

    onFocus() {
      this.isFocused = true;
      this.showLocation = true;
    },

    onBlur() {
      this.isFocused = false;
      this.inputOrigin = this.origin.name;
    },

    handleProductClick(product, index) {
      this.$emit("segment", this.segment.onClickEvent("product_selected"));
      this.activeProduct = index;
      this.setProductType(product);
    },

    switchToSubscription(){
      const startDate = new Date(this.starts);
      const endDate = new Date(this.ends);

      endDate.setHours(startDate.getHours());
      endDate.setMinutes(startDate.getMinutes());
      endDate.setSeconds(startDate.getSeconds());

      this.$store.commit("Search/setDateTime", {
          point: "starts",
          date: startDate,
      });
      this.$store.commit("Search/setDateTime", {
          point: "ends",
          date: endDate,
      });
      this.setSubscription(true)
      this.handleGetCarClick();
    }
  },
  watch: {
    city(newCity) {
      this.fetchSuggested();
    },
    countries(newCountries) {
      if (
        (this.countries && !this.origin.name) ||
        (this.origin.name && !this.origin.lat)
      ) {
        const cityInfo = this.countries[0]?.cities?.find(
          (city) => city.city_link_name === this.city
        );
        this.homeProducts = this.homeProductsTabs;
        if (cityInfo?.home_ingress?.length) {
          this.inputOrigin = cityInfo?.home_ingress?.[0]?.location?.address;
          this.$store.commit("Search/setOrigin", {
            name: cityInfo?.home_ingress[0]?.location?.name,
            lat: cityInfo?.home_ingress[0]?.location?.lat,
            lng: cityInfo?.home_ingress[0]?.location?.lng,
          });
        }
      }
    },
    origin(newOrigin) {
      this.inputOrigin = this.origin.name;
    },

    showCalendar(newValue) {
      if (newValue) {
        this.$store.commit("App/setAppPopupState", true);
      } else {
        this.$store.commit("App/setAppPopupState", false);
      }
    },

    showLocation(newValue) {
      if (newValue) {
        this.$store.commit("App/setAppPopupState", true);
      } else {
        this.$store.commit("App/setAppPopupState", false);
      }
    },

    showCityPopup(newValue) {
      if (newValue) {
        this.$store.commit("App/setAppPopupState", true);
      } else {
        this.$store.commit("App/setAppPopupState", false);
      }
    },
    isSubscription(newVal){
      this.activeProduct = newVal ? 1 : 0;
    }
  },
};
