.home-offers {
    width: 100%;

    &-card {
        width: 344px;
        border-radius: $padding08;
        border: $border-default;
        background: $background-color-inverse;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding: $padding12;
        position: relative;

        &-info {
            display: flex;
            min-height: 95px;
            align-items: flex-start;
            justify-content: flex-start;
            flex-direction: column;
            width: 100%;
            border-bottom: $border-default;
            border-bottom-style: dashed;
            gap: $padding12;

            &-code {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: row;
                gap: $padding12;

                &-icon {
                    img {
                        width: $padding32;
                        height: $padding32;
                        object-fit: contain;
                    }
                }

                &-text {
                    @include title1_medium;
                }
            }

            &-text {
                width: 224px;

                &-info {
                    @include body2;
                    color: $label-color-secondary;
                }
            }

            &-tag {
                position: absolute;
                top: $padding00;
                right: $padding12;

                &-container {
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    &-text {
                        @include caption;
                        color: $label-color-inverse;
                        text-align: center;
                        position: absolute;
                        width: 79px;
                    }
                }
            }
        }

        &-button {
            @include button1;
            color: $label-color-info;
            padding: $padding20 $padding00 $padding12;
            align-items: center;
            text-align: center;
            border: none;
            background-color: white;
            cursor: pointer;

        }
    }

    .home-carousel {
        padding: $padding24;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        background-color: $background-color-inverse;
        margin: $padding00 auto;
        border-radius: $padding16;
        width: 85%;
    }

    .home-carousel-container-arrow-right {
        cursor: pointer;
        margin: $padding00 $padding12 $padding00 $padding32;
        display: flex;
        padding: $padding04 $padding12;
        align-items: center;
        gap: $padding12;
        border-radius: $padding32;
        border: $border-default;
        background: $background-color-inverse;
        @include h3;
    }

    .home-carousel-container-arrow-left {
        cursor: pointer;
        margin: $padding00 $padding32 $padding00 $padding12;
        display: flex;
        padding: $padding04 $padding12;
        align-items: center;
        gap: $padding12;
        border-radius: $padding32;
        border: $border-default;
        background: $background-color-inverse;
        @include h3;
    }
}

@media screen and (max-width: $mobile-max-width) {
    .home-offers {

        &-card {
            &-button {
                padding: $padding12 $padding00 $padding00;
            }
        }
    
        .home-carousel {
            padding: $padding16 $padding00 $padding16 $padding16;
            margin: $padding00;
            border-radius: $padding00;
            width: 100%;
        }
    
        .home-carousel-container-arrow-right {
            display: none;
        }
    
        .home-carousel-container-arrow-left {
            display: none;
        }
    }
}