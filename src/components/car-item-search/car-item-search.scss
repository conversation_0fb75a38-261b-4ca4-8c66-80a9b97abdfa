@mixin textStyles(
  $fontSize,
  $fontWeight,
  $lineHeight,
  $letterSpacing,
  $fontColor
) {
  font-size: $fontSize;
  font-weight: $fontWeight;
  line-height: $lineHeight;
  letter-spacing: $letterSpacing;
  color: $fontColor;
}
.car-search-list {
  max-height: 100%;
  // overflow-y: scroll;
  display: grid;
  grid-template-columns: repeat(auto-fit, 330px);
  column-gap: 20px;
  row-gap: 32px;
  .fare {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    &__popup {
      background: white;
      border-radius: $padding16;
      width: 90%;
      max-width: 400px;
      max-height: 90vh;
      overflow-y: auto;
    }

    &__header {
      padding: $padding16;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__title {
      @include h5;
    }

    &__close {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: $background-color-primary;
      font-size: $padding24;
      padding: $padding08;
      cursor: pointer;
      border: none;
    }

    &__content {
      display: flex;
      flex-direction: column;
      gap: $padding20;
      padding: $padding16;
    }

    &__items {
      display: flex;
      flex-direction: column;
      gap: $padding16;
    }

    &__group {
      border: $border-default;
      padding: 0 $padding12 $padding12;
      border-radius: $padding08;
    }

    &__group-header {
      @include title2;
      color: $label-color-success;
      background-color: $background-color-success;
      position: relative;
      top: -$padding12;
      padding: $padding04;
      display: inline-block;
    }

    &__detail {
      display: flex;
      justify-content: space-between;
      gap: $padding04;
      margin-bottom: $padding08;

      &-header {
        color: #666;
        @include title1-medium;

        &--secondary {
          @include body2;
        }
      }

      &-value {
        @include title1-medium;

        &--highlighted {
          color: $label-color-success;
          font-weight: bold;
        }
      }
    }

    &__info {
      display: flex;
      gap: $padding20;
      flex-direction: column;
    }

    &__info-section {
      border: $border-default;
      border-radius: $padding08;
      padding: $padding04 $padding08 $padding08;
    }

    &__info-header {
      position: relative;
      top: -$padding12;
      padding: $padding04;
      @include title2;
      display: inline-block;

      &--primary {
        color: $label-color-warning;
        background-color: $background-color-warning;
      }

      &--additional {
        color: $label-color-error;
        background-color: $background-color-error;
      }
    }

    &__info-list {
      margin: 0;
      padding-left: $padding16;
      list-style-type: disc;
    }

    &__info-item {
      margin-bottom: $padding08;
      @include body2;
      color: $label-color-secondary;
    }
  }
}

@media screen and (min-width: 320px) {
  .car-item-search-container {
    position: relative;
    height: fit-content;
    &-tag-container {
      position: absolute;
      display: flex;
      gap: 10px;
      margin-left: 10px;
      margin-top: 10px;
      &-tag {
        background-color: $background-color-successDark;
        z-index: 5;
        border-radius: 4px;
        padding: 4px 8px;
        top: 10px;
        left: 10px;
        width: fit-content;
        @include textStyles(12px, 600, 1.33, 0.4px, white);

        &.new {
          background-color: $background-color-warningDark;
        }
      }
    }
    &-host {
      display: flex;
      padding: $padding08 $padding08;
      align-items: center;
      gap: $padding02;
      align-self: stretch;
      background: linear-gradient(
        89deg,
        #d9f6ff 33.27%,
        #eef8fb 69.81%,
        rgba(255, 255, 255, 0) 98.33%
      );
      &-image {
        border-radius: 50%;
        width: $padding24;
        height: $padding24;
        border: 1px solid var(--Aqua-Teal-Aqua-Teal-30, #50bdd5);
      }
      &-name {
        @include overline;
        color: var(--Aqua-Teal-Aqua-Teal-40, #3b8a9c);
        font-style: italic;
      }
    }
    cursor: pointer;
    font-family: "IBM Plex Sans";
    border-radius: 24px;
    background: white;
    width: 330px;
    margin-right: auto;
    margin-left: auto;
    border: $border-secondary;
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    &-image-container {
      position: relative;
      width: 100%;
      &-slider {
        width: 100%;
        overflow: hidden;
        border-radius: $padding20;
        &-list {
          cursor: pointer;
          display: flex;
          width: 300%;
          &-image {
            height: 220px;
            // width: 33.33%;
            object-fit: cover;
          }
        }
        .gradient-overlay {
          width: 100%;
          height: 100%;
          border-radius: $padding20;
          position: absolute;
          left: 0;
          top: 0;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 62.5%,
            #000 80.5%
          );
          pointer-events: none;
        }
      }

      &-action {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: $padding04;
        &-rating {
          white-space: nowrap;
          display: flex;
          padding: 4px 6px;
          align-items: center;
          gap: 6px;
          flex-shrink: 0;
          border-radius: 4px;
          background: $background-color-successDark;
          i {
            font-size: $padding16;
            color: $label-color-inverse;
          }
          &-text {
            display: flex;
            gap: 5px;
            align-items: center;
            &-line {
              width: 1px;
              height: 10px;
              flex-shrink: 0;
              color: $label-color-inverse;
              background-color: $background-color-inverse;
            }
            span {
              @include title2-medium;
              color: $label-color-inverse;
            }
            span:nth-of-type(2) {
              @include body2;
              color: $label-color-inverse;
            }
          }
        }
        .guest-fav {
          border-radius: $padding04;
          border: 0.5px solid $label-color-warning;
          background: linear-gradient(90deg, #000 0%, #434343 100%);
          i {
            color: $label-color-warning;
          }
        }
        .bad {
          border-radius: $padding04;
          background: $background-color-warningDark;
        }
        .new {
          border-radius: $padding04;
          background-color: $background-color-infoDark;
        }
        &-button {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 48px;
          width: 48px;
          border-radius: 50%;
          background-color: #10a310;
          cursor: pointer;
          // z-index: 3;
        }
      }
      &-tags {
        position: absolute;
        top: 5%;
        width: 100%;
        padding: 0 5%;
        display: flex;
        justify-content: space-between;
        &-tag {
          @include textStyles(12px, normal, 1.33, 0.4px, white);
          background: black;
          color: white;
          border-radius: 9px;
          padding: 2px 8px;
          &-disable {
            visibility: hidden;
            background: none;
            padding: 0;
          }
        }
      }
      &-zoomcar-assured {
        display: inline-flex;
        padding: $padding04 $padding08;
        align-items: center;
        gap: $padding08;
        border-radius: 6px;
        background-color: $background-color-infoDark;
        position: absolute;
        top: 5%;
        left: 5%;
        border-radius: 8px;
        span {
          @include overline;
          color: $label-color-inverse;
        }
      }
      &-guest-fav {
        // background-color: #000;
        // z-index: 0;
        // padding: 1px 5px;
        // border-radius: 8px;

        @include overline_medium;
        color: $label-color-warning;
        text-align: right;
        img {
          width: 100px;
          height: $padding12;
        }
      }
      &-new {
        @include overline_medium;
        color: $label-color-info;
      }
      &-bad {
        @include overline_medium;
        color: $label-color-error;
      }
      &-rating-text {
        @include title2_medium;
        color: $label-color-success;
      }
      &-fav {
        z-index: 1;
        font-size: 20px;
        position: absolute;
        top: 5%;
        right: 5%;
        i {
          color: white;
          &.fav {
            color: $crimson-red-30;
          }
        }
        .outer-fav {
          position: relative;
          left: $padding24;
          z-index: -1;
          color: #00000080;
        }
      }
      &-dots {
        display: flex;
        gap: 2px;
        height: 10px;
        &-dot {
          width: $padding04;
          height: $padding04;
          border: 1px solid;
          border-radius: 50%;
          border: none;
          &-disable {
            // visibility: hidden;
            background-color: #ffffff33;
          }
          &-green {
            background-color: white;
            scale: 1.8;
          }
        }
      }
    }
    &-info-container {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      position: absolute;
      bottom: 0px;
      width: 100%;
      padding: $padding12;
    }
    &-car-info {
      // position: absolute;
      // top: 53%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      // padding: 8px 10px 6px 10px;
      &-host-offer {
        display: flex;
        position: relative;
        align-items: center;
        top: -15px;
        color: white;
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 12px;
        font-size: 12px;
        border-radius: 16px;
        gap: 10px;
        margin-bottom: -15px;
        background-image: linear-gradient(
          to left,
          $background-color-successDark,
          $rich-green-40
        );
        &.disabled {
          background-image: none;
          background-color: $background-color-dot;
        }
        i {
          font-size: 20px;
        }
      }
      &-left {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        &-title {
          @include textStyles(16px, 600, 24px, 0.16px, #ffffff);
          min-width: 160px;
          @media screen and (max-width: 380px) {
            @include overline_medium;
            color: $label-color-inverse;
          }
        }
        &-details {
          @include textStyles(12px, 400, 24px, 0, #ffffff);
          // padding-bottom: 10px;
          display: flex;
          align-items: center;
          // margin-top: 2px;
          &-item {
            display: flex;
            align-items: center;
            margin-right: 5px;
            @include body2;
            color: $label-color-inverse;
            &-dot {
              margin-left: 5px;
              height: 0.5px;
              width: 0.5px;
              background-color: lightgray;
              border: 1px solid;
              border-radius: 50%;
            }
          }
        }
      }
      &-revenue {
        display: flex;
        justify-content: flex-end;
        &-price {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: $padding08;
          justify-content: space-between;
          &-hourly {
            display: flex;
            flex-direction: row;
            align-items: baseline;
            gap: $padding04;
            justify-content: flex-end;
          }
          &-discounted {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 2px;
            &-amount {
              @include title1-medium;
            }
            &-unit {
              @include title1-medium;
            }
          }
          &-original {
            color: $label-color-error;
            font-family: "IBM Plex Sans";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            text-decoration: line-through;
          }
          &-discount {
            display: flex;
            height: 15px;
            padding: 0px 8px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 2px;
            background: $background-color-successDark;
            span {
              color: $label-color-inverse;
              font-family: "IBM Plex Sans";
              font-size: $padding12;
              font-style: normal;
              font-weight: 600;
              line-height: normal;
            }
          }
        }
        &-distance {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          padding: 2px 4px;
          border-radius: 2px;
          border: $border-default;
          background-color: $background-color-success;
          height: fit-content;
          color: $label-color-secondary;
          font-size: 12px;
          i {
            color: $label-color-success;
            font-size: 16px;
            padding-right: 2px;
          }
        }
        &-total {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          white-space: nowrap;
          span {
            @include body2;
            color: $label-color-secondary;
            text-decoration: underline;
          }
        }
      }
    }
    .pop-up-container {
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0%;
      left: 0;
      opacity: 0;
      overflow: hidden;
      -webkit-overflow-scrolling: touch;
      z-index: -10;
      transition: all 0.1s ease-out;
      .close-btn {
        height: 32px;
        background-color: #e0e0e0;
        border-radius: 50%;
        width: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        position: absolute;
        top: -50px;
        right: 16px;
      }
      .bg-transparent {
        height: 100vh;
        position: fixed;

        height: 100%;
        width: 100%;
        top: 0%;
        left: 0;
        opacity: 0.87;
        background-color: $background-color-primaryDark;
      }

      &.fare-summary {
        .bg-transparent {
          height: 100vh;
          position: fixed;
        }
        .slide-up {
          height: fit-content;
          position: fixed;

          .pop-up-content {
            max-height: 500px;
          }
        }
        .popup-header {
          font-size: 20px;
          font-weight: 600;
        }
        .fare {
          border-bottom: $border-default;
          padding-top: 15px;
          padding-bottom: 15px;
          display: flex;
          flex-direction: column;
          gap: 10px;
        }
        .fare-item {
          display: flex;
          justify-content: space-between;

          &.highlighted {
            font-weight: 600;
          }
        }
        .btn-container {
          margin-top: 20px;
        }
      }
      .pop-up-content-container {
        padding: 16px;
        box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2),
          0 6px 30px 5px rgba(0, 0, 0, 0.12),
          0 16px 24px 2px rgba(0, 0, 0, 0.14);
        background-color: $background-color-inverse;
        position: absolute;
        width: 100%;
        bottom: -100%;
        opacity: 1;
        height: auto;
        transition: all 0.6s ease-out;
        .popup-header {
          display: flex;
          padding-bottom: 16px;
          .content {
            flex: 2;
            padding-right: 16px;
            .popup-heading {
              @include title-1-primary;
            }
            .popup-sub-heading {
              @include caption-secondary;
            }
          }
          .logo {
            img {
              width: 40px;
            }
          }
        }
        .pop-up-content {
          padding-top: 8px;
          overflow: auto;
          max-height: 420px;
        }
        &.slide-up {
          bottom: 0%;
        }
      }
    }
    &-border-line {
      margin: 0px 10px;
      border-bottom: 1px dashed $border-color-primary;
    }
    &-price {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: $padding16;
      gap: 8px;
    }
    &-features {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      // padding: 8px 10px 6px 10px;
      gap: 8px;
      &-top {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 4px;
        i {
          font-size: 16px;
          color: $label-color-primary;
        }
        img {
          width: 16px;
          height: 16px;
        }
        &-title {
          color: $label-color-primary;
          font-family: "IBM Plex Sans";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
      }
      &-mid {
        display: flex;
        align-items: flex-end;
        gap: 4px;
        &-container {
          display: flex;
          // padding: 4px 6px;
          justify-content: center;
          align-items: center;
          gap: 6px;
          align-self: stretch;
          border-radius: 4px;
          // border: 1px solid $border-color-primary;
          i {
            background: $background-color-successDark;
            padding: $padding04;
            border-radius: $padding04;
            color: $label-color-inverse;
          }
          span {
            color: $label-color-primary;
            font-family: "IBM Plex Sans";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            text-transform: uppercase;
          }
        }
      }
      &-bottom {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 4px;
        border-radius: 8px;
        background: $background-color-info;
        gap: 4px;
        i {
          font-size: 16px;
        }
        &-title {
          color: $label-color-primary;
          font-family: "IBM Plex Sans";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
    &-stats {
      border-top: 1px solid #e0e0e0;
      border-bottom: 1px solid #e0e0e0;
      margin: 10px 16px 0px 16px;
      padding: 10px 0px;
      display: flex;
      gap: 4px;
      @include textStyles(12px, normal, 1.33, 0.4px, #1f1f1f);
    }

    &-delivery {
      display: flex;
      gap: 10px;
      padding: 0 16px;
      margin-top: 20px;
      &-product {
        display: flex;
        padding: 2px 5px;
        border-radius: 16px;
        width: max-content;
        gap: 3px;
        align-items: center;
        font-size: 12px;
      }
      &-hd {
        background-color: #fff5db;
        color: #a35c00;
      }
      &-fastag {
        background-color: $background-color-error;
        color: $label-color-error;
      }
      &-airport {
        background-color: #f0f4ff;
        color: #5160c2;
      }
    }
  }
}

.car-item-search-carousal-arrows-arrow {
  display: none;
}
@media screen and (min-width: 901px) {
  .car-item-search-container {
    z-index: 2;
    display: block;
    // max-width: 328px;
    &-image-container {
      &-list {
        &-image {
          width: 100%;
        }
      }
    }

    &:hover {
      .car-item-search-carousal-arrows {
        position: absolute;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        &-arrow {
          cursor: pointer;
          width: 46px;
          height: 46px;
          // padding: 16px;
          opacity: 0.8;
          border-radius: 100%;
          background-color: #000;
          display: flex;
          justify-content: center;
          align-items: center;
          &-hide {
            display: none;
          }
          &.left-arrow {
            margin-right: auto;
            margin-left: 10px;
            z-index: 3;
          }
          &.right-arrow {
            margin-left: auto;
            margin-right: 10px;
            z-index: 3;
          }
        }
      }
    }
  }
}
