<template>
  <div class="car-item-search">
    <section
      class="car-item-search-container"
      :id="car.car_data && car.car_data.car_id"
      @click="handleCarClick"
    >
      <section class="car-item-search-container-image-container">
        <!-- @click="handleClick($event)" -->

        <!-- <div
        v-if="car.car_data.tags"
        class="car-item-search-container-tag-container"
      >
        <div
          v-for="tag in car.car_data.tags"
          :key="tag.text"
          :class="[
            'car-item-search-container-tag-container-tag',
            { new: tag.type && tag.type.toLowerCase().includes('new') },
          ]"
        >
          {{ tag.text }}
        </div>
      </div> -->
        <section
          v-if="!Carousel && isMobile"
          class="car-item-search-container-image-container-slider"
        >
          <img
            class="car-item-search-container-image-container-slider-list-image"
            :src="car.car_data.image_urls[0]"
            @error="replaceByDefault"
          />
          <div class="gradient-overlay"></div>
        </section>
        <section
          v-else
          class="car-item-search-container-image-container-slider"
        >
          <!-- Arrows -->
          <div class="car-item-search-carousal-arrows">
            <div
              :class="{
                'car-item-search-carousal-arrows-arrow left-arrow': true,
                'car-item-search-carousal-arrows-arrow-hide': tab === 0,
              }"
              @click="handleCarouselArrowClick('left', $event)"
            >
              <img src="/img/icons/left-arrow-white.svg" />
            </div>
            <div
              :class="{
                'car-item-search-carousal-arrows-arrow right-arrow': true,
                'car-item-search-carousal-arrows-arrow-hide':
                  tab === car.car_data.image_urls.length - 1,
              }"
              @click="handleCarouselArrowClick('right', $event)"
            >
              <img src="/img/icons/right-arrow-white.svg" />
            </div>
          </div>

          <section
            :id="
              'car-item-search-container-image-container-slider-list-' + itemId
            "
            class="car-item-search-container-image-container-slider-list"
            :style="{
              width: `${car.car_data.image_urls.length * 100}%`,
            }"
          >
            <!-- v-for="image in car.car_data.image_urls" -->
            <img
              class="car-item-search-container-image-container-slider-list-image"
              :style="{ width: `${100 / car.car_data.image_urls.length}%` }"
              v-for="(image, index) in car.car_data.image_urls"
              :key="image"
              :src="handleSource(index, image)"
              @error="replaceByDefault"
              @touchstart="handleTouchStart"
              @touchend="handleTouchEnd"
              loading="lazy"
              :alt="`car images of ${car?.car_data?.name}`"
            />
            <div class="gradient-overlay"></div>
          </section>
          <!-- src="https://zoomcar-assets.zoomcar.com/photographs/original/a49559a67ba1d5fd7e2ffea2f78f654691ee018e.png?1663874345" -->
        </section>

        <!-- :src="car.car_data.url_large" -->

        <section class="car-item-search-container-image-container-tags">
          <div
            :class="{
              'car-item-search-container-image-container-tags-tag': true,
              'car-item-search-container-image-container-tags-tag-disable': car
                .car_data.tag
                ? car.car_data.tag.length === 0
                : true,
            }"
          >
            {{ car.car_data.tag }}
          </div>
        </section>
        <section
          v-if="zoomcar_assured"
          class="car-item-search-container-image-container-zoomcar-assured"
        >
          <img
            class="car-item-search-container-image-container-zoomcar-assured-icon"
            src="/img/zoomcar_assured.svg"
            alt="Zoomcar Assured"
          />
          <span>Zoomcar Assured</span>
        </section>

        <section
          class="car-item-search-container-image-container-fav"
          v-if="showFav"
        >
          <FavButton :isFav="isFav" @toggle="handleFavClick(isFav)" />
        </section>
        <div class="car-item-search-container-info-container">
          <section class="car-item-search-container-car-info">
            <div class="car-item-search-container-car-info-left">
              <section
                class="car-item-search-container-image-container-dots"
                :class="{}"
              >
                <div
                  v-for="index in visibleDots"
                  :key="index"
                  :class="{
                    'car-item-search-container-image-container-dots-dot': true,
                    'car-item-search-container-image-container-dots-dot-green':
                      tab < 5 ? index - 1 === tab : index === 5,
                    'car-item-search-container-image-container-dots-dot-disable':
                      !car.car_data.is_original_car_images,
                  }"
                ></div>
              </section>

              <div class="car-item-search-container-car-info-left-title">
                <!-- Maruti Suzuki Swift -->
                {{ car.car_data.name }}
              </div>
              <div class="car-item-search-container-car-info-left-details">
                <div
                  class="car-item-search-container-car-info-left-details-item"
                  v-for="(item, index) in car.car_data.accessories"
                  :key="item"
                >
                  {{ item }}

                  <div
                    v-if="index < car.car_data.accessories.length - 1"
                    class="car-item-search-container-car-info-left-details-item-dot"
                  ></div>
                </div>
              </div>
            </div>
          </section>
          <!-- Ratings-section -->
          <section
            v-if="car.car_data && car.car_data.rating"
            class="car-item-search-container-image-container-action"
          >
            <section
              :class="{
                'car-item-search-container-image-container-action-rating': true,
                'guest-fav':
                  car.car_data.decoration &&
                  car.car_data.decoration === 'guest_favourite',
                bad: car.car_data.rating && car.car_data.rating.type === 'BAD',
                new: car.car_data.rating && car.car_data.rating.type === 'NEW',
              }"
            >
              <section
                v-if="car.car_data && car.car_data.rating"
                class="car-item-search-container-image-container-action-rating-text"
              >
                <i
                  :class="{
                    'z-crown':
                      car.car_data.decoration &&
                      car.car_data.decoration === 'guest_favourite',
                    'z-star_fill':
                      car.car_data.decoration !== 'guest_favourite',
                  }"
                ></i>
                <span>{{
                  car.car_data.rating.value || car.car_data.rating.type
                }}</span>
                <span v-if="car.car_data.rating.trips"
                  >({{ car.car_data.rating.trips }})</span
                >
              </section>
            </section>
            <span
              v-if="
                car.car_data.rating &&
                car.car_data.rating.type === 'NEW' &&
                car.car_data.rating.value
              "
              class="car-item-search-container-image-container-new"
            >
              {{ car.car_data.rating.text }}</span
            >
            <span
              v-else-if="
                car.car_data.rating &&
                car.car_data.rating.type === 'GUEST_FAVORITE'
              "
              class="car-item-search-container-image-container-guest-fav"
              ><img src="/img/Guest_Favourite.svg" alt="Guest Favourite" />
            </span>
            <span
              v-else-if="
                car.car_data.rating &&
                car.car_data.rating.type === 'BAD' &&
                car.car_data.rating.value
              "
              class="car-item-search-container-image-container-bad"
            >
            </span>
            <span
              v-else-if="car.car_data.rating.value"
              class="car-item-search-container-image-container-rating-text"
            >
              {{
                car.car_data.rating.text ||
                car.car_data.rating.color.charAt(0).toUpperCase() +
                  car.car_data.rating.color.slice(1).toLowerCase()
              }}</span
            >
          </section>
        </div>
      </section>
      <section
        class="car-item-search-container-car-info-host-offer"
        v-if="
          car.car_data.host_offers && car.car_data.host_offers.host_offer_data
        "
        :class="{
          disabled:
            car.car_data.host_offers &&
            car.car_data.host_offers.host_offer_data &&
            car.car_data.host_offers.host_offer_data.is_enabled !== true,
        }"
      >
        <i class="z-coupon"></i>
        <div class="desc">
          {{ car.car_data.host_offers.host_offer_data.title }}
        </div>
      </section>

      <section class="car-item-search-container-price">
        <div class="car-item-search-container-features" v-if="car.car_data.delivery_info">
          <div
            v-for="item in car.car_data.delivery_info"
            :key="item.text"
            class="car-item-search-container-features-top"
          >
            <!-- <div class="car-item-search-container-features-top"> -->
            <!-- <i :class="`z-${item.icon.toLowerCase()}`"></i> -->
            <img
              :src="item.image"
              :alt="item.text"
              class="car-item-search-container-features-top-icon"
            />
            <span class="car-item-search-container-features-top-title">{{
              item.text
            }}</span>
          </div>
        </div>
        <div class="car-item-search-container-car-info-revenue">
          <div class="car-item-search-container-car-info-revenue-price">
            <div
              v-if="car.car_data.discount"
              class="car-item-search-container-car-info-revenue-price-discount"
            >
              <span>{{ car.car_data.discount }}</span>
            </div>
            <div
              class="car-item-search-container-car-info-revenue-price-hourly"
            >
              <div
                v-if="car.car_data.price_line_1"
                class="car-item-search-container-car-info-revenue-price-original"
              >
                <!-- ₹18,938 -->
                {{ car.car_data.price_line_1.striked_text }}
              </div>
              <div
                v-if="car.car_data.price_line_1"
                class="car-item-search-container-car-info-revenue-price-discounted"
              >
                <span
                  class="car-item-search-container-car-info-revenue-price-discounted-amount"
                  >{{
                    getPriceAmount(car.car_data.price_line_1.normal_text)
                  }}</span
                >
                <span
                  class="car-item-search-container-car-info-revenue-price-discounted-unit"
                  >{{
                    getPriceUnit(car.car_data.price_line_1.normal_text)
                  }}</span
                >
              </div>
            </div>
            <div
              v-if="car.car_data.pricing && car.car_data.price_line_2"
              class="car-item-search-container-car-info-revenue-total"
              @click.stop="handlePriceLineClick"
            >
              <span> {{ car.car_data.price_line_2.header }}</span>
            </div>
          </div>
        </div>

        <!-- <div class="car-item-search-container-features-bottom">
        <img :src="discountIcon" alt="">
        <span class="car-item-search-container-features-bottom-title">Extra 10% off - ZoomCar</span>
      </div> -->
      </section>
      <section
        class="car-item-search-container-host"
        v-if="car.car_data.host_info"
      >
        <img
          class="car-item-search-container-host-image"
          :src="car.car_data.host_info.icon"
          alt=" Host Image"
          v-if="car.car_data.host_info.icon"
        />
        <span class="car-item-search-container-host-name">
          {{ car.car_data.host_info.text }}
        </span>
      </section>

      <!-- <section
        class="car-item-search-container-image-container-action-button"
      >
        <img src="/img/icons-cart-2.svg" />
      </section> -->
      <!-- <section class="car-item-search-container-stats">
      <section class="car-item-search-container-stats-icon">
        <img src="/img/icons-top-speed.svg" />
      </section>
      <section class="car-item-search-container-stats-text">
        Booked 6 Times In Last 1 Hour
      </section>
    </section> -->

      <!-- <section
      v-if="car.car_data.location && car.car_data.location.text"
      class="car-item-search-container-revenue-distance"
    >
      <i class="z-walk" /> {{ car.car_data.location.text }}
    </section> -->

      <!-- <section class="car-item-search-container-delivery">
      <div
        :class="{
          'car-item-search-container-delivery-product': true,
          'car-item-search-container-delivery-airport':
            product.id.toLowerCase() === 'airport',
          'car-item-search-container-delivery-hd':
            product.id.toLowerCase() === 'hd',
          'car-item-search-container-delivery-fastag':
            product.id.toLowerCase() === 'fastag',
        }"
        v-for="product in car.car_data.add_ons"
        :key="product.id"
      >
        <i
          v-if="product.id.toLowerCase() === 'fastag'"
          class="z-parking_fee_2"
        ></i>

        <img v-else :src="`/img/icons/icons-${product.id.toLowerCase()}.svg`" />
        <div class="car-item-search-container-delivery-product-text">
          {{ productHash[product.label] }}
        </div>
      </div>
    </section> -->
    </section>
    <div
      v-if="showSummaryPopup && showFareSummary"
      class="fare"
      @click="closeFarePopup"
    >
      <div class="fare__popup" @click.stop>
        <div class="fare__header">
          <div class="fare__title">PRICE BREAKUP</div>
          <button class="fare__close" @click="closeFarePopup">
            <i class="z-close"></i>
          </button>
        </div>

        <div class="fare__content">
          <div
            v-if="car.car_data.price_line_2.fare_breakup"
            class="fare__items"
          >
            <div
              v-for="(fareGroup, index) in car.car_data.price_line_2
                .fare_breakup.fare_items"
              :key="'fare-group-' + index"
              class="fare__group"
            >
              <span class="fare__group-header"> INCLUDED </span>
              <div
                v-for="(item, itemIndex) in fareGroup.fare_item"
                :key="'fare-item-' + itemIndex"
                class="fare__detail"
              >
                <span
                  :class="[
                    'fare__detail-header',
                    { 'fare__detail-header--secondary': item.value == ' ' },
                  ]"
                  >{{ item.header }}</span
                >
                <span
                  :class="[
                    'fare__detail-value',
                    {
                      'fare__detail-value--highlighted':
                        item.is_highlighted === 'true',
                    },
                  ]"
                >
                  {{ item.value }}
                </span>
              </div>
            </div>
          </div>

          <div
            v-if="car.car_data.price_line_2.fare_breakup.fare_info"
            class="fare__info"
          >
            <div
              class="fare__info-section"
              v-if="
                car.car_data.price_line_2.fare_breakup.fare_info
                  .fare_primary_info
              "
            >
              <div class="fare__info-header fare__info-header--primary">
                {{
                  car.car_data.price_line_2.fare_breakup.fare_info
                    .fare_primary_info.title
                }}
              </div>
              <ul class="fare__info-list">
                <li
                  v-for="(msg, idx) in car.car_data.price_line_2.fare_breakup
                    .fare_info.fare_primary_info.messages"
                  :key="'primary-' + idx"
                  class="fare__info-item"
                >
                  {{ msg }}
                </li>
              </ul>
            </div>

            <div
              class="fare__info-section"
              v-if="
                car.car_data.price_line_2.fare_breakup.fare_info
                  .fare_additional_info
              "
            >
              <div class="fare__info-header fare__info-header--additional">
                {{
                  car.car_data.price_line_2.fare_breakup.fare_info
                    .fare_additional_info.title
                }}
              </div>
              <ul class="fare__info-list">
                <li
                  v-for="(msg, idx) in car.car_data.price_line_2.fare_breakup
                    .fare_info.fare_additional_info.messages"
                  :key="'additional-' + idx"
                  class="fare__info-item"
                >
                  {{ msg }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "./car-item-search.scss";
import CarItemSearch from "./car-item-search.js";
export default CarItemSearch;
</script>
