.home-carousel {
    padding: $padding24;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: $background-color-primary;
    margin: $padding36 auto;
    border-radius: $padding16;
    width: 85%;

    &-header {
        display: flex;
        width: 100%;
        white-space: nowrap;
        align-items: center;
        justify-content: center;
        gap: $padding16;
        margin: $padding20 $padding00;

        .needle {
            border-radius: $padding00 $padding04 $padding04 $padding00;
            width: 100%;
            max-width: $padding64;
            height: $padding04;
            background: linear-gradient(90deg, $background-color-plum 0%, $background-color-plumDark 100%);
            clip-path: polygon(0% 50%, 100% 0%, 100% 100%);

            &-2 {
                border-radius: $padding00 $padding04 $padding04 $padding00;
                width: 100%;
                max-width: $padding64;
                height: $padding04;
                background: linear-gradient(90deg, $background-color-plum 0%, $background-color-plumDark 100%);
                clip-path: polygon(0% 50%, 100% 0%, 100% 100%);
                transform: scaleX(-1);
            }
        }

        &-text {
            @include h2;
            text-align: center;
        }
    }
    &-filters-container {
        display: flex;
        gap: $padding08;
        width: 100%;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        @media screen and (max-width: $mobile-max-width) {
          overflow: auto;
          padding: $padding08 0;
          justify-content: flex-start;
          flex-wrap: nowrap;
        }
      }
    &-container {

        margin: $padding24 $padding00;
        display: grid;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        grid-template-columns: auto auto auto;



        &-arrow-left {
            font-size: $padding32;
            cursor: pointer;
            padding: $padding00 $padding40 $padding00 $padding12;
            text-align: right;

            .inactive {
                color: $label-color-inactive ;
                cursor: default;
            }
        }

        &-arrow-right {
            font-size: $padding32;
            cursor: pointer;
            padding: $padding00 $padding12 $padding00 $padding40;
            text-align: left;

            .inactive {
                color: $label-color-inactive ;
                cursor: default;
            }
        }

        &-visible {
            display: flex;
            align-items: center;
            overflow: hidden;
            width: 100%;
        }

        &-carousel {
            display: flex;
            align-items: stretch;
            flex-direction: row;
            justify-content: start;
            gap: $padding20;
            width: 100%;
            position: relative;
        }
    }

    &-dots {
        text-align: center;
        display: flex;
        gap: $padding12;

        &-dot {
            width: $padding08;
            height: $padding08;
            background-color: $background-color-dot;
            border-radius: 50%;

            &-active {
                width: $padding20;
                height: $padding08;
                border-radius: $padding16;
                background-color: $background-color-successDark;
            }
        }
    }

    &-button {
        background-color: $background-color-black;
        margin-top: $padding20;
        padding: $padding16 $padding48;
        border-radius: $padding04;
        @include title1_medium;
        text-transform: uppercase;
        color: $label-color-inverse;
        cursor: pointer;

    }
}

@media screen and (max-width: $mobile-max-width) {
    .home-carousel {
        padding: $padding16 $padding00 $padding16 $padding16;
        background-color: $background-color-inverse;
        margin: $padding00;
        width: 100%;
        justify-content: flex-start;

        &-header {
            width: 100%;
            gap: $padding16;
            margin: $padding00;
            justify-content: flex-start;
            padding: $padding00;

            .needle {
                display: none;
            }
            .needle-2{
                flex: 1;
                max-width:100%;
                margin-right: $padding08;
            }

            &-text {
                @include title2_medium;
                font-weight: 600;
                text-transform: uppercase;
                text-align: left;
            }
        }

        &-container {
            grid-template-columns: 1fr;
            margin: $padding00;
            padding: $padding00;

            &-arrow-left {
                display: none;
            }

            &-arrow-right {
                display: none;
            }

            &-visible {
                overflow: scroll;
                padding: $padding16 $padding00;
            }

            &-carousel {
                gap: $padding16;
            }

            &-carousel-element {
                border-radius: $padding12;
                &:last-child {
                    padding-right: $padding16;
                }
            }
        }

        &-dots {
            display: none;
        }

        &-button {
            width: calc(100% - 16px);
            text-align: center;
            margin-right: $padding16;
        }
    }
}