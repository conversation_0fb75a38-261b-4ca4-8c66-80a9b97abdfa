import carModelCityData from "~/data/car-model-city.json";
import { mapGetters, mapActions } from "vuex";
import { addMinutes } from "date-fns";
import { microsite } from "~/helpers/microsite/utils";
import cookies from "~/helpers/cookies";
import segment from "./analytics";
import { bajajFinservSession } from "~/helpers/microsite/bajajfinserv";
import { airindiaSession } from "~/helpers/microsite/airindia";
import { airindiaexpressSession } from "~/helpers/microsite/airindiaexpress";
import { yonosbiSession } from "~/helpers/microsite/yonosbi";
import {
  AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY,
  BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
  DISPLAY_MICROSITE_APPLY_OFFER_POPUP,
  AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
  Y<PERSON><PERSON>_SBI_APPLY_REFERRAL_COUPON_CODE_KEY,
} from "~/helpers/microsite/constants";
export default {
  name: "CityCarModel",

  components: {
    HeroBannerNew: () =>
      import("~/components/hero-banner-new/hero-banner-new.vue"),
    Stories: () => import("~/components/stories/stories.vue"),
    HomeCarousel: () => import("~/components/home-carousel/home-carousel.vue"),
    BlogSvg: () => import("~/pages/home-rebrand/blog-svg.vue"),
    TitleWithNeedle: () => import("~/components/title-with-needle"),
    AccordionHome: () =>
      import("~/components/accordion-home/accordion-home.vue"),
    TopCities: () => import("~/components/top-cities"),
    FooterNew: () => import("~/components/footer-new/footer-new.vue"),
    HostAndAppDownload: () => import("~/components/host-and-app-download"),
    NewCarCard: () => import("~/components/new-car-card"),
    HomeMwebSearch: () => import("~/components/home-mweb-search"),
    CarItemSearch: () => import("~/components/car-item-search"),
  },

  data() {
    return {
      heroBannerData: {
        title: "",
        subtitle: "",
        image: "",
      },
      blogs: [],
      blogHeading: "Places to explore around",
      cars: [],
      carsData: [],
      geoAddress: {},
      isApiCalled: false,
    };
  },

  computed: {
    ...mapGetters({
      city: "App/city",
      origin: "Search/origin",
      product: "Search/product",
      minDate: "Search/minStarts",
      suggested: "Search/suggested",
      starts: "Search/starts",
      ends: "Search/ends",
      userCityLinkName: "User/userCityLinkName",
      cities: "Location/cities",
      country: "App/country",
      countryCode: "App/countryCode",
      isCitiesLoaded: "Location/isCitiesLoaded",
      locationCalendarTab: "HomeNew/locationCalendarTab",
      cityDetail: "Location/cityDetail",
      selectedCar: "HomeNew/selectedCar",
      isMobile: "App/isMobile",
    }),

    segment() {
      return segment.call(this);
    },

    routeParams() {
      const cityLink = this.city || this.$route.params.city_link;
      const city = cityLink.charAt(0).toUpperCase() + cityLink.slice(1);

      const carModelObject = carModelCityData["car-models"].find(
        (car) =>
          car["route-param"].toLowerCase() === this.$route.params.car_model
      );

      return {
        countryCode: this.$route.params.country_code,
        city: city,
        city_link: cityLink,
        carModel: this.$route.params.car_model,
        car_model: carModelObject["car-model"],
      };
    },
    heroBannerPreTitle() {
      return this.$options.filters.replacePlaceholders(
        this.heroBannerData.title,
        this.routeParams
      );
    },
    heroBannerTitle() {
      return this.$options.filters.replacePlaceholders(
        this.heroBannerData.subtitle,
        this.routeParams
      );
    },
    faqs() {
      return carModelCityData["landing-page"].faqs;
    },
    otherUrls() {
      return carModelCityData["car-models"].filter(
        (model) =>
          model["route-param"].toLowerCase() !==
          this.routeParams.carModel.toLowerCase()
      );
    },
    carModelData() {
      return carModelCityData["car-models"].find(
        (car) =>
          car["route-param"].toLowerCase() ===
          this.routeParams.carModel.toLowerCase()
      );
    },
    carModelInfo() {
      return this.carModelData?.content?.[this.routeParams.city_link];
    },
    cityDisplay() {
      return this.city
        ? `${this.city.charAt(0).toUpperCase()}${this.city.slice(1)}`
        : this.routeParams.city;
    },
  },

  mounted() {
    this.$emit("segment", this.segment.onPageLoad);
    this.initializeData();
    this.makeApiCalls();
  },

  methods: {
    ...mapActions({
      fetchGeoAddress: "Search/fetchGeoAddress",
      fetchWebSections: "HomeNew/fetchWebSections",
      fetchJWTToken: "User/fetchJWTToken",
      fetchNudgeContent: "HomeNew/fetchNudgeContent",
      fetchCarList: "HomeNew/fetchCarList",
      fetchFavIds: "User/fetchFavIds",
      fetchCarSuggestions: "Home/fetchCarSuggestions",
      fetchTrendingSearch: "Search/fetchTrendingSearch",
    }),

    initializeData() {
      const modelData = this.carModelData;
      const landingPageData = carModelCityData["landing-page"];

      if (modelData) {
        // Set banner data
        this.heroBannerData = {
          title: landingPageData["hero-banner"].title,
          subtitle: landingPageData["hero-banner"].subtitle,
        };
      }
    },

    makeApiCalls() {
      this.fetchCars();
      this.fetchBlogs();
      this.setSearchQuery();
      this.fetchFavIds().catch(() => {});

      this.$store.commit("Search/setGeoSessionToken");
    },

    fetchCars() {
      if (
        this.isApiCalled || !this.starts || !this.ends ||
        !this.origin.lat || !this.origin.lng
      ) {
        return;
      }

      this.isApiCalled = true;

      this.fetchCarSuggestions()
        .then((res) => {
          const { cache_key } = res?.pagination;
          this.fetchTrendingSearch({
            type: "search",
            text: this.carModelData["brand"],
            cache_key: cache_key,
          }).then((res) => {
            let totalResults = 0;
            const carModel = this.carModelData["model"].toLowerCase();
            const carModelWithoutSpaces = carModel.replace(/\s/g, "");

            const suitedResults =
              res?.results?.filter((result) => {
                if (totalResults > 5) return false;
                let res =
                  result.header.toLowerCase().includes(carModel) ||
                  result.header.toLowerCase().includes(carModelWithoutSpaces);
                if (res) {
                  totalResults += result.count;
                }
                return res;
              }) || [];

            // array of promises for each suited result
            const carPromises = suitedResults.map((result) =>
              this.fetchCarSuggestions({ fts: result.value })
                .then((carResponse) => {
                  if (carResponse?.empty_response == null) {
                    return carResponse.cars || [];
                  }
                  return [];
                })
                .catch(() => [])
            );

            // Wait for all car suggestions to be fetched and merge results
            Promise.all(carPromises)
              .then((carArrays) => {
                this.cars = carArrays.flat();
                let lat = this.origin.lat;
                let lng = this.origin.lng;

                this.carsData = this?.cars.map((item) => {
                  return {
                    car_data: {
                      ...item,
                      location_new: item.location,
                      image_urls: item.images,
                      pricing: {
                        payable_amount: item.price_line_1.normal_text,
                        fare_header: item.price_line_2.header,
                      },
                      add_ons: item.old_checkout_params.available_add_ons.map(
                        (item) => ({
                          label: item,
                          id: item,
                        })
                      ),
                      location: {
                        prime_location_id:
                          item.old_checkout_params.prime_location_id,
                        location_id: item.old_checkout_params.location_id,
                        lat,
                        lng,
                        start: this.starts,
                        ends: this.ends,
                        // distance: 1.2590015012609357,
                        // text: "1.9 km away",
                        hd_ids: [],
                        terminal_ids: [],
                        // zoom_air_unvailable_reason: "LEAD_TIME",
                      },
                    },
                  };
                });
              })
              .catch(() => {
                this.cars = [];
                this.carsData = [];
              });
          });
        })
        .catch((err) => {
          this.cars = [];
        })
        .finally(() => {
          this.isApiCalled = false;
        });
    },
    lastCar(index) {
      return index === this.carsData.length - 1;
    },
    handleFavClick() {
      this.$emit("toast", "Login to mark the car as favourite", "error");
    },
    handleCarSelection(carDetails, car) {
      const { Cart } = this.$store.state;

      //first_call is being set as true
      //the idea is that the checkout api is being called for first time when the user checks the checkout summary
      //for the case of returning back to search page from checkout summary page and opening a new car's checkout summary
      //    - it does not trigger a reload, so first_call is false in that case for a new car
      //    - this causes the cta_enabled property as false resulting in checkout button as disabled

      Cart.first_call = true;

      const { city_link } = this.$route.params;
      const pricings = car.pricing || car.pricings;
      const priceIndex = pricings
        ? pricings
            .map((p, i) => ({ ...p, index: i }))
            .filter((p) => p.id == carDetails.price_id)
            .map((p) => p.index)[0]
        : null;

      const addOns = car.car_data.add_ons
        ? car.car_data?.add_ons?.map((item) => item.id)
        : [];

      Cart.data = {
        car: car.car_data.name,
        brand: car.car_data.brand,
        car_id: carDetails.car_id,
        cargroup_id: carDetails.cargroup_id,
        car_price: carDetails.car_price,
        city: city_link,
        starts: carDetails.starts ? carDetails.starts : this.starts,
        ends: carDetails.ends ? carDetails.ends : this.ends,
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        hd: 0,
        offer_id: carDetails.offer_id ? carDetails.offer_id : null,
        radius: carDetails.radius,
        exactLocation: "",
        terminal_id: carDetails.terminal_id,
        hd_location_ids: "",
        user_city: "",
        user_address_id: "",
        flexi_id: { 0: 5, 1: 10, 2: 15 }[priceIndex],
        last_selected_option: {},
        offer_params: null,
        promo: "",
        section_params: [],
        deal: null,
        seater: carDetails.seater,
        transmission: carDetails.transmission,
        flex_name: carDetails.flex_name,
        hd_unavailable_reason: carDetails.location.hd_unavailable_reason,
        add_ons: addOns,
      };

      if (carDetails.offer_id) {
        Cart.data = {
          offer_params: {
            offer_id: carDetails.offer_id,
          },
        };
      }

      Cart.data = {
        location_id: carDetails.location_id || carDetails.starting_location_id,
        location_name: carDetails.locName,
        type: "round_trip",
        hd_location_ids:
          (carDetails.location.hd_ids.length && carDetails.location.hd_ids) ||
          null,
        hd: (carDetails.location.hd_ids.length && 1) || 0,
        // location_name: encodeURIComponent(this.origin.name),
        lat: this.origin.lat,
        lng: this.origin.lng,
        user_address_id: 0,
        edit: false,

        //reset HD data on new car selection
        address_id: "",
        terminal_id: "",
        section_params: [],
      };

      this.$emit("segment", this.segment.bookNowClick(Cart.data));

      if (microsite()) {
        if (bajajFinservSession()) {
          sessionStorage.setItem(
            BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaSession()) {
          sessionStorage.setItem(AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaexpressSession()) {
          sessionStorage.setItem(
            AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (yonosbiSession()) {
          sessionStorage.setItem(YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        }
      }

      const params = {
        city_link,
        country_code: this.$store.getters["App/countryCode"],
        car_id: Cart.data.car_id,
        car_name: Cart.routeCarName,
      };

      const query = {
        location_id: carDetails.location.location_id,
        starts: (carDetails.starts
          ? carDetails.starts
          : this.starts
        )?.getTime(),
        ends: (carDetails.ends ? carDetails.ends : this.ends)?.getTime(),
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        cargroup_id: carDetails.cargroup_id,
      };
      const referrer = sessionStorage.getItem("referrer");
      if (referrer) {
        query.referrer = referrer;
      }

      if (!cookies.read("authToken")) {
        this.$emit("segment", this.segment.bookNowClickNonLoggedInUser());
        Cart.data.address = this.geoAddress.display_name;
        Cart.data.zipcode = this.geoAddress.postcode;
        if (microsite() || this.isMobile) {
          this.$router.push({
            name: "CarDetailPage",
            params,
            query,
          });
        } else {
          const route = this.$router.resolve({
            name: "CarDetailPage",
            params,
            query,
          });
          window.open(route.href, "_blank");
        }
      } else {
        Cart.data.selected_terminal = "";

        this.fetchGeoAddress({
          coords: { lat: this.origin.lat, lng: this.origin.lng },
        })
          .then((res) => {
            if (res && res[0]) {
              Cart.data.address = res[0].display_name;
              Cart.data.zipcode = res[0].postcode;
            }
          })
          .catch((err) => {
            return err;
          });
        Cart.data.isTerminal = false;

        if (Cart.data.hd == 0) {
          if (microsite() || this.isMobile) {
            this.$router.push({
              name: "CarDetailPage",
              params,
              query,
            });
          } else {
            const route = this.$router.resolve({
              name: "CarDetailPage",
              params,
              query,
            });
            window.open(route.href, "_blank");
          }
        } else {
          this.fetchGeoAddress({
            coords: { lat: this.origin.lat, lng: this.origin.lng },
          })
            .then((res) => {
              if (res && res[0]) {
                Cart.data.address = res[0].display_name;
                Cart.data.locality = res[0].district;
              }
            })
            .catch((err) => {
              return err;
            });
          this.$router.push({
            name: "CarDetailPage",
            params: {
              city_link,
              country_code: this.$store.getters["App/countryCode"],
            },
          });
        }
      }
    },

    fetchBlogs(withoutCity = false) {
      let blogsApiUrl = "https://www.zoomcar.com/blogs/in/wp-json/wp/v2/posts";
      let city = this.city || this.$route.params.city_link || "bangalore";
      city = `${city[0].toUpperCase()}${city.slice(1, city.length)}`;
      const query = {
        category: "popular-destinations-in",
        orderby: "date",
        order: "desc",
        per_page: 10,
        search: withoutCity ? "" : city,
        _embed: true,
      };
      const queryString = new URLSearchParams(query).toString();

      fetch(`${blogsApiUrl}?${queryString}`, {
        method: "GET",
      })
        .then((res) => res.json())
        .then((response) => {
          if (response.length < 3) {
            this.blogs = [];
            this.blogHeading = "Top Travel Destinations";
            this.fetchBlogs(true);
            return;
          }
          this.blogHeading = withoutCity
            ? "Top Travel Destinations"
            : `Top Travel Destinations Near ${city}`;
          let blogData = response.map((blog) => {
            let minimizedTitle =
              blog.yoast_head_json?.title || blog.title?.rendered || "";
            try {
              minimizedTitle = minimizedTitle.split("- Zoomcar")[0];
            } catch (e) {}
            return {
              id: blog.id,
              image: blog.yoast_head_json?.og_image?.[0]?.url,
              title: minimizedTitle,
              link: blog.link,
              subTitle:
                blog.yoast_head_json?.description || blog.excerpt?.rendered,
            };
          });
          this.blogs = [...blogData];
        })
        .catch((e) => {
          this.blogs = [];
        });
    },

    handleBlogCtaClick() {
      this.$emit(
        "segment",
        this.segment.onBlogClick("https://www.zoomcar.com/blogs/in")
      );
      window.open("https://www.zoomcar.com/blogs/in", "_blank");
    },

    handleBlogCardClick(card) {
      this.$emit("segment", this.segment.onBlogClick(blog.link));
      window.open(blog.link, "_blank");
    },

    // handleSegment(event) {
    //   this.$emit("segment", this.segment.onClickEvent(event));
    // },
    handleSegment(event, data) {
      if (event === "image_swiped") {
        this.$emit("segment", this.segment.imageSwiped(data));
      } else if (event == "screenLoaded") {
        this.$emit("segment", this.segment.onPageLoad(data));
      } else {
        this.$emit("segment", this.segment.onClickEvent(data));
      }
    },
    setSearchQuery() {
      let query = {
        lat: this.origin.lat,
        lng: this.origin.lng,
        type: this.product,
        starts: this.starts?.getTime(),
        ends: this.ends?.getTime(),
        car_id: this.selectedCar.search_params["seek.car_id"],
        city_id: this.selectedCar.search_params["seek.city_id"],
        lat_e5: this.selectedCar.search_params["seek.lat_e5"],
        lng_e5: this.selectedCar.search_params["seek.lng_e5"],
        location_id: this.selectedCar.search_params["seek.location_id"],
        cargroup_id: this.selectedCar.cargroup_id,
      };
      const searchRoute = {
        name: "SearchPage",
        query: query,
        params: {
          city_link: this.userCityLinkName,
          country_code: this.countryCode,
        },
      };

      sessionStorage.setItem("searchRoute", JSON.stringify(searchRoute));
    },

    handleCarClick(car) {
      this.$emit("segment", this.segment.onCarClick(car.car_id));
      const { Cart } = this.$store.state;
      Cart.first_call = true;
      const { city_link } = this.$route.params || "bangalore";
      Cart.data = {
        car: car.name,
        car_id: car.car_id,
        cargroup_id: car.cargroup_id,
        car_price: car.price_line_2.raw,
        city: city_link,
        starts: car.starts ? car.starts : this.starts,
        ends: car.ends ? car.ends : this.ends,
        lat: car.lat || this.origin.lat,
        lng: car.lng || this.origin.lng,
        hd: 0,
        offer_id: car.offer_id ? car.offer_id : null,
        exactLocation: "",
        hd_location_ids: "",
        user_city: "",
        user_address_id: "",
        last_selected_option: {},
        offer_params: null,
        promo: "",
        section_params: [],
        deal: null,
        seater: car.accessories[2],
        transmission: car.accessories[0],
        flex_name: car.flex_name || "",
        hd_unavailable_reason: car.location.hd_unavailable_reason || "",
        add_ons: car.old_checkout_params.available_add_ons,
      };

      Cart.data = {
        prime_location_id: car.old_checkout_params.prime_location_id,
        location_id: car.old_checkout_params.location_id,
        location_name: this.origin.name,
        type: "round_trip",
        hd_location_ids: null,
        hd: 0,
        lat: this.origin.lat,
        lng: this.origin.lng,
        user_address_id: 0,
        edit: false,

        //reset HD data on new car selection
        address_id: "",
        terminal_id: "",
        section_params: [],
      };

      const params = {
        city_link,
        country_code: this.$store.getters["App/countryCode"],
        car_id: Cart.data.car_id,
        car_name: Cart.routeCarName,
      };

      const query = {
        location_id: car.old_checkout_params.location_id,
        starts: (car.starts ? car.starts : this.starts)?.getTime(),
        ends: (car.ends ? car.ends : this.ends)?.getTime(),
        lat: car.lat || this.origin.lat,
        lng: car.lng || this.origin.lng,
        cargroup_id: car.cargroup_id,
      };

      this.$router.push({
        name: "CarDetailPage",
        params,
        query,
      });

      this.fetchGeoAddress({
        coords: { lat: this.origin.lat, lng: this.origin.lng },
      })
        .then((res) => {
          if (res && res[0]) {
            Cart.data.address = res[0].display_name;
            Cart.data.zipcode = res[0].postcode;
          }
        })
        .catch((err) => {
          return err;
        });
    },
  },

  watch: {
    city() {
      this.fetchCars();
      this.fetchBlogs();
    },
    origin(newOrigin, oldOrigin) {
      if (!oldOrigin.starts) {
        this.$store.commit("Search/updateOriginStarts", {
          starts: addMinutes(new Date(), 2 * 7 * 24 * 60),
        });
        this.fetchCars();
        this.setSearchQuery();
      }
    },
    starts() {
      this.fetchCars();
      this.setSearchQuery();
    },
    ends() {
      this.fetchCars();
      this.setSearchQuery();
    },
  },
};
