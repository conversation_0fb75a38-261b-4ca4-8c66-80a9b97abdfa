import segment from "./analytics/segment";
import HomeCarousel from "~/components/home-carousel";
import homeCityData from "../../data/home_city.json";
import Shimmer from "~/components/shimmer";
import TopCategories from "~/components/top-categories";
import Stories from "~/components/stories";
import BlogSvg from "./blog-svg.vue";
import { v4 as uuidv4 } from "uuid";
import faq_city from "~/data/faq-homepage/faq-city.json";
import { mapActions, mapState, mapGetters } from "vuex";
import cookies from "~/helpers/cookies";
import * as ab from "~/helpers/ab-experiment";
import { isMobile } from "~/config/defaults";
import { setItemListSchema } from "~/routes/schema";
import { addMinutes } from "date-fns";

/** Microsite related Imports ** Start ** */
import SmartbuyBanner from "~/components/smartbuy-banner";
import NewCarCard from "~/components/new-car-card";
import AirindiaBanner from "~/components/airindia-banner";
import AirindiaexpressBanner from "~/components/airindiaexpress-banner";
import BajajFinservBanner from "~/components/bajajfinserv-banner";
import YonosbiBanner from "~/components/yonosbi-banner";
import faq_smartbuy from "~/data/faq-homepage/faq-smartbuy.json";
import faq_airindia from "~/data/faq-homepage/faq-airindia.json";
import faq_airindiaexpress from "~/data/faq-homepage/faq-airindiaexpress.json";
import { smartbuySession } from "~/helpers/microsite/smartbuy.js";
import { airindiaSession } from "~/helpers/microsite/airindia.js";
import { airindiaexpressSession } from "~/helpers/microsite/airindiaexpress.js";
import { yonosbiSession } from "~/helpers/microsite/yonosbi.js";
import { bajajFinservSession } from "~/helpers/microsite/bajajfinserv.js";
import { wegoSession } from "../../helpers/microsite/wego";
import {
  AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY,
  BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
  DISPLAY_MICROSITE_APPLY_OFFER_POPUP,
  AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
  YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY,
} from "~/helpers/microsite/constants";
import { microsite } from "~/helpers/microsite/utils";
import FooterNew from "~/components/footer-new";
import throttle from "~/helpers/throttle";
import SubscriptionPopup from "~/components/subscription-popup";
import CarItemSearch from "../../components/car-item-search";
import seoDataCity from "~/data/seo_data_city.json";
/** Microsite related Imports ** End ** */

const { API_URL } = process.env;

export default {
  name: "HomeRebrand",
  components: {
    NewCarCard,
    CarItemSearch,
    HomeCarousel,
    Shimmer,
    TopCategories,
    Stories,
    BlogSvg,
    SmartbuyBanner,
    AirindiaBanner,
    AirindiaexpressBanner,
    BajajFinservBanner,
    YonosbiBanner,
    FooterNew,
    SubscriptionPopup,
    HeroBannerB: () =>
      import(
        /* webpackChunkName: "hero-banner-b" */ "~/components/hero-banner-b"
      ),
    AccordionHome: () =>
      import(
        /* webpackChunkName: "accordion-home" */ "~/components/accordion-home/accordion-home.vue"
      ),
    HomeOffers: () =>
      import(/* webpackChunkName: "home-offers" */ "~/components/home-offers"),
    HomeMwebSearch: () =>
      import(
        /* webpackChunkName: "home-mweb-search" */ "~/components/home-mweb-search"
      ),
    MwebPopup: () =>
      import(/* webpackChunkName: "mweb-popup" */ "~/components/mweb-popup"),
    WegoBanner: () =>
      import(/* webpackChunkName: "microsite" */ "~/components/wego-banner"),
    FloatingYoutubePlayer: () =>
      import(
        /* webpackChunkName: "floating-youtube-player" */ "~/components/floating-youtube-player"
      ),
    FeaturedIn: () => import(/* webpackChunkName: "featured-in" */ "~/components/featured-in"),
  },
  data() {
    return {
      carMakes: [
        { image: "/img/hompage-rebrand/jeep.png", make: "Jeep" },
        { image: "/img/hompage-rebrand/mercedes.png", make: "Mercedes" },
        { image: "/img/hompage-rebrand/bmw.png", make: "BMW" },
        { image: "/img/hompage-rebrand/porsche.png", make: "Porsche" },
        { image: "/img/hompage-rebrand/jeep.png", make: "Jeep" },
        { image: "/img/hompage-rebrand/mercedes.png", make: "Mercedes" },
        { image: "/img/hompage-rebrand/bmw.png", make: "BMW" },
        { image: "/img/hompage-rebrand/porsche.png", make: "Porsche" },
      ],
      marketingBanners: [],
      cities_cards: [
        {
          image: "/img/hompage-rebrand/top-cities/Bangalore.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Bangalore.jpg",
          city: "Bangalore",
          link: "bangalore",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Jaipur.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Jaipur.jpg",
          city: "Jaipur",
          link: "jaipur",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Kolkata.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Kolkata.jpg",
          city: "Kolkata",
          link: "kolkata",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Chennai.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Chennai.jpg",
          city: "Chennai",
          link: "chennai",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Delhi.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Delhi.jpg",
          city: "Delhi",
          link: "delhi",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Goa.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Goa.jpg",
          city: "Goa",
          link: "goa",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Hyderabad.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Hyderabad.jpg",
          city: "Hyderabad",
          link: "hyderabad",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Mumbai.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Mumbai.jpg",
          city: "Mumbai",
          link: "mumbai",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Vizag.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Vizag.jpg",
          city: "Vizag",
          link: "vizag",
        },
        {
          image: "/img/hompage-rebrand/top-cities/Pune.webp",
          fallbackImage: "/img/hompage-rebrand/top-cities/Pune.jpg",
          city: "Pune",
          link: "pune",
        },
      ],
      car_categories_imgs: {
        SUV: {
          webp: "/img/car-images/suv.webp",
          img: "/img/car-images/suv.jpg",
        },
        SEDAN: {
          webp: "/img/car-images/sedan.webp",
          img: "/img/car-images/sedan.jpg",
        },
        ELECTRIC: {
          webp: "/img/car-images/ev.webp",
          img: "/img/car-images/ev.jpg",
        },
        HATCHBACK: {
          webp: "/img/car-images/hatchback.webp",
          img: "/img/car-images/hatchback.jpg",
        },
      },
      blogs: [],
      blogHeading: "Places to explore around",
      cars: [],
      carsData: [],
      geoAddress: {},
      results: [],
      loading: true,
      error: null,
      showQRPopup: false,
      city_data: homeCityData,
      //microsite
      smartbuy: smartbuySession(),
      airindia: airindiaSession(),
      airindiaexpress: airindiaexpressSession(),
      yonosbi: yonosbiSession(),
      bajajfinserv: bajajFinservSession(),
      wegobanner: wegoSession(),
      scrollPosition: 0,
      observer: null,
      viewMore: true,
      cityPages: [
        "bangalore",
        "delhi",
        "hyderabad",
        "chennai",
        "pune",
        "mumbai",
        "kolkata",
        "goa",
        "jaipur",
        // "kochi",
        // "coimbatore",
        // "ahmedabad"
      ],
      showMwebPopup: true,
      showVideo: false,
      timeout: null,
      showSubscriptionPopup: false,
      isApiCalled: false,
    };
  },

  computed: {
    ...mapGetters({
      city: "App/city",
      origin: "Search/origin",
      product: "Search/product",
      isSubscription: "Search/isSubscription",
      //userCityLinkName: "User/userCityLinkName",
      minDate: "Search/minStarts",
      suggested: "Search/suggested",
      starts: "Search/starts",
      ends: "Search/ends",
      userCityLinkName: "User/userCityLinkName",
      cities: "Location/cities",
      country: "App/country",
      countryCode: "App/countryCode",
      isCitiesLoaded: "Location/isCitiesLoaded",
      locationCalendarTab: "HomeNew/locationCalendarTab",
      cityDetail: "Location/cityDetail",
      selectedCar: "HomeNew/selectedCar",
      isMobile: "App/isMobile",
      isSubscription: "Search/isSubscription",
    }),
    segment() {
      return segment.call(this);
    },
    faqs() {
      //smartbuy
      if (smartbuySession()) return faq_smartbuy;
      //air india
      else if (airindiaSession()) return faq_airindia;
      // air india express
      else if (airindiaexpressSession()) return faq_airindiaexpress;
      // yono-sbi
      else if (yonosbiSession()) return faq_airindiaexpress;
      //city page
      return faq_city?.faq_questions;
    },

    cityDisplay() {
      return this.city ?
      `${this.city.charAt(0).toUpperCase()}${this.city.slice(1)}`:
      "City";
    },

    micrositeFlow() {
      return microsite();
    },

    allianceFlow() {
      const pid = JSON.parse(sessionStorage.getItem("pid")) || "";
      return pid?.indexOf("Alliances_Airtel") > -1 || false;
    },

    shortTermCards() {
      if (this.city_data[this.city].short_term.cards_data.cards) {
        if (!this.isMobile) {
          if (
            this.city_data[this.city].short_term.cards_data.cards.length > 3
          ) {
            if (this.viewMore)
              return this.city_data[
                this.city
              ].short_term.cards_data.cards.slice(0, 3);
            else return this.city_data[this.city].short_term.cards_data.cards;
          }
        } else {
          if (
            this.city_data[this.city].short_term.cards_data.cards.length > 2
          ) {
            if (this.viewMore)
              return this.city_data[
                this.city
              ].short_term.cards_data.cards.slice(0, 1);
            else return this.city_data[this.city].short_term.cards_data.cards;
          }
        }
      }
    },
    isCityData() {
      return this.cityPages.includes(this.city);
    },

    shouldShowMwebPopup() {
      if (!isMobile()) {
        return false;
      }
      if (this.micrositeFlow || this.allianceFlow) {
        return false;
      }
      if (this.$route.query.q !== undefined) {
        return false;
      }
      const version = ab.getVisitorVariation()
      if(version == 1) {
        return false
      }

      return true;
    },
  },

  created() {
    if(this.$route.name == "LandingPageNew") {
      return;
    }

    const {city_link, country_code} = this.$route.params;
    const url = API_URL + "/v8/search/home";
    const start_millis = new Date(addMinutes(new Date(), 24 * 60))
    const end_millis = new Date(addMinutes(start_millis, 48 * 60))

    const lat = seoDataCity[city_link]?.lat
    const lng = seoDataCity[city_link]?.lng

    //checkif lat lng is present then only proceed
    if(!lat || !lng) {
      return;
    }

    const params = new URLSearchParams({
      start_epoch_millis: start_millis?.getTime(),
      end_epoch_millis: end_millis?.getTime(),
      type: "normal",
      lat: lat,
      lng: lng,
      city: city_link,
      category: "-100",
      device_id: localStorage.getItem("device_id"),
      platform: "android",
      country_code: "IND",
      version: "4",
      locale: "en",
    });
    const headers = {};

    return fetch(`${url}?${params}`, {
      method: "GET",
      headers: headers,
    })
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
        return []
      }
      return response.json();
    })
    .then((data) => {
      const defaultParams = {
        city_link: city_link,
        country_code: country_code,
      }

      const defaultQuery = {
        lat: lat,
        lng: lng,
        starts: start_millis,
        ends: end_millis,
      }

      setItemListSchema(data?.cars || [], defaultParams, defaultQuery)
    })
    .catch((error) => {
      
    });
  },

  mounted() {
    this.$emit("segment", this.segment.onPageLoad);
    this.fetchCarSuggetions();
    this.fetchBlogs();
    this.setSearchQuery();
    this.fetchFavIds().catch(() => {});

    this.$store.commit("Search/setGeoSessionToken");
    this.visibilityObserver();
    this.throttledScroll = throttle(this.handleScroll, 200);
    window.addEventListener("scroll", this.throttledScroll);

    this.triggerClarityCustomEvent();
    // this.logicToShowPipVideo()

    //if due to any issue scroll is disabled, we need to set the popup state to false
    this.$store.commit("App/setAppPopupState", false);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.throttledScroll);
    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.timeout) {
      clearTimeout();
    }
    this.showVideo = false;
  },
  methods: {
    ...mapActions({
      fetchGeoAddress: "Search/fetchGeoAddress",
      fetchWebSections: "HomeNew/fetchWebSections",
      fetchJWTToken: "User/fetchJWTToken",
      fetchNudgeContent: "HomeNew/fetchNudgeContent",
      fetchCarList: "HomeNew/fetchCarList",
      fetchFavIds: "User/fetchFavIds",
      setSubscription: "Search/setSubscription",
    }),
    setSearchQuery() {
      let query = {
        lat: this.origin.lat,
        lng: this.origin.lng,
        type: this.product,
        starts: this.starts?.getTime(),
        ends: this.ends?.getTime(),
        car_id: this.selectedCar.search_params["seek.car_id"],
        city_id: this.selectedCar.search_params["seek.city_id"],
        lat_e5: this.selectedCar.search_params["seek.lat_e5"],
        lng_e5: this.selectedCar.search_params["seek.lng_e5"],
        location_id: this.selectedCar.search_params["seek.location_id"],
        cargroup_id: this.selectedCar.cargroup_id,
      };
      const searchRoute = {
        name: "SearchPage",
        query: query,
        params: {
          city_link: this.userCityLinkName,
          country_code: this.countryCode,
        },
      };

      sessionStorage.setItem("searchRoute", JSON.stringify(searchRoute));
    },

    fetchCarSuggetions() {
      if (
        this.isApiCalled || !this.starts || !this.ends ||
        !this.origin.lat || !this.origin.lng
      ) {
        return;
      }
      this.isApiCalled = true;
      const url = API_URL + `/v8/search/${this.isSubscription ? 'zoomplus' : 'home'}`;
      const start_millis = new Date(this.starts);
      const end_millis = new Date(this.ends);

      const params = new URLSearchParams({
        start_epoch_millis: start_millis?.getTime(),
        end_epoch_millis: end_millis?.getTime(),
        type: "normal",
        lat: this.origin.lat,
        lng: this.origin.lng,
        city: this.city,
        category: "-100",
        device_id: localStorage.getItem("device_id"),
        platform: "android",
        country_code: "IND",
        version: "15.8.0 debug",
        locale: "en",
      });
      const headers = {};

      if (this.starts)
        fetch(`${url}?${params}`, {
          method: "GET",
          headers: headers,
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error("Network response was not ok");
            }
            return response.json();
          })
          .then((data) => {
            //this.results = JSON.parse(data);
            let lat = this.origin.lat;
            let lng = this.origin.lng;

            this.carsData = data?.cars.map((item) => {
              return {
                car_data: {
                  ...item,
                  location_new: item.location,
                  image_urls: item.images,
                  pricing: {
                    payable_amount: item.price_line_1.normal_text,
                    fare_header: item.price_line_2.header,
                  },
                  add_ons: item.old_checkout_params.available_add_ons.map(
                    (item) => ({
                      label: item,
                      id: item,
                    })
                  ),
                  location: {
                    prime_location_id:
                      item.old_checkout_params.prime_location_id,
                    location_id: item.old_checkout_params.location_id,
                    lat,
                    lng,
                    start: this.starts,
                    ends: this.ends,
                    // distance: 1.2590015012609357,
                    // text: "1.9 km away",
                    hd_ids: [],
                    terminal_ids: [],
                    // zoom_air_unvailable_reason: "LEAD_TIME",
                  },
                },
              };
            });
            this.cars = data?.cars;
            this.setSchema(data?.cars)
          })
          .catch((error) => {
            this.error = "Failed to load results.";
            console.error(error);
          })
          .finally(() => {
            this.isApiCalled = false;
            this.loading = false;
          });
    },
    lastCar(index) {
      return index === this.carsData.length - 1;
    },

    fetchBlogs(withoutCity = false) {
      let blogsApiUrl = "https://www.zoomcar.com/blogs/in/wp-json/wp/v2/posts";
      let city = this.city || this.$route.params.city_link || "bangalore";
      city = `${city[0].toUpperCase()}${city.slice(1, city.length)}`;
      const query = {
        category: "popular-destinations-in",
        orderby: "date",
        order: "desc",
        per_page: 10,
        search: withoutCity ? "" : city,
        _embed: true,
      };
      const queryString = new URLSearchParams(query).toString();

      fetch(`${blogsApiUrl}?${queryString}`, {
        method: "GET",
      })
        .then((res) => res.json())
        .then((response) => {
          if (response.length < 3) {
            this.blogs = [];
            this.blogHeading = "Top Travel Destinations";
            this.fetchBlogs(true);
            return;
          }
          this.blogHeading = withoutCity
            ? "Top Travel Destinations"
            : `Top Travel Destinations Near ${city}`;
          let blogData = response.map((blog) => {
            let minimizedTitle =
              blog.yoast_head_json?.title || blog.title?.rendered || "";
            try {
              minimizedTitle = minimizedTitle.split("- Zoomcar")[0];
            } catch (e) {}
            return {
              id: blog.id,
              image: blog.yoast_head_json?.og_image?.[0]?.url,
              title: minimizedTitle,
              link: blog.link,
              subTitle:
                blog.yoast_head_json?.description || blog.excerpt?.rendered,
            };
          });
          this.blogs = [...blogData];
        })
        .catch((e) => {
          this.blogs = [];
        });
    },

    handleBlogCtaClick() {
      this.$emit(
        "segment",
        this.segment.onBlogClick("https://www.zoomcar.com/blogs/in")
      );
      window.open("https://www.zoomcar.com/blogs/in", "_blank");
    },

    handleBlogCardClick(blog) {
      this.$emit("segment", this.segment.onBlogClick(blog.link));
      window.open(blog.link, "_blank");
    },

    handleCarSelection(carDetails, car) {
      const { Cart } = this.$store.state;

      //first_call is being set as true
      //the idea is that the checkout api is being called for first time when the user checks the checkout summary
      //for the case of returning back to search page from checkout summary page and opening a new car's checkout summary
      //    - it does not trigger a reload, so first_call is false in that case for a new car
      //    - this causes the cta_enabled property as false resulting in checkout button as disabled

      Cart.first_call = true;

      const { city_link } = this.$route.params;
      const pricings = car.pricing || car.pricings;
      const priceIndex = pricings
        ? pricings
            .map((p, i) => ({ ...p, index: i }))
            .filter((p) => p.id == carDetails.price_id)
            .map((p) => p.index)[0]
        : null;

      const addOns = car.car_data.add_ons
        ? car.car_data?.add_ons?.map((item) => item.id)
        : [];

      Cart.data = {
        car: car.car_data.name,
        brand: car.car_data.brand,
        car_id: carDetails.car_id,
        cargroup_id: carDetails.cargroup_id,
        car_price: carDetails.car_price,
        city: city_link,
        starts: carDetails.starts ? carDetails.starts : this.starts,
        ends: carDetails.ends ? carDetails.ends : this.ends,
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        hd: 0,
        offer_id: carDetails.offer_id ? carDetails.offer_id : null,
        radius: carDetails.radius,
        exactLocation: "",
        terminal_id: carDetails.terminal_id,
        hd_location_ids: "",
        user_city: "",
        user_address_id: "",
        flexi_id: { 0: 5, 1: 10, 2: 15 }[priceIndex],
        last_selected_option: {},
        offer_params: null,
        promo: "",
        section_params: [],
        deal: null,
        seater: carDetails.seater,
        transmission: carDetails.transmission,
        flex_name: carDetails.flex_name,
        hd_unavailable_reason: carDetails.location.hd_unavailable_reason,
        add_ons: addOns,
      };

      if (carDetails.offer_id) {
        Cart.data = {
          offer_params: {
            offer_id: carDetails.offer_id,
          },
        };
      }

      Cart.data = {
        location_id: carDetails.location_id || carDetails.starting_location_id,
        location_name: carDetails.locName,
        type: "round_trip",
        hd_location_ids:
          (carDetails.location.hd_ids.length && carDetails.location.hd_ids) ||
          null,
        hd: (carDetails.location.hd_ids.length && 1) || 0,
        // location_name: encodeURIComponent(this.origin.name),
        lat: this.origin.lat,
        lng: this.origin.lng,
        user_address_id: 0,
        edit: false,

        //reset HD data on new car selection
        address_id: "",
        terminal_id: "",
        section_params: [],
      };

      this.$emit("segment", this.segment.bookNowClick(Cart.data));

      if (microsite()) {
        if (bajajFinservSession()) {
          sessionStorage.setItem(
            BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaSession()) {
          sessionStorage.setItem(AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaexpressSession()) {
          sessionStorage.setItem(
            AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (yonosbiSession()) {
          sessionStorage.setItem(YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        }
      }

      const params = {
        city_link,
        country_code: this.$store.getters["App/countryCode"],
        car_id: Cart.data.car_id,
        car_name: Cart.routeCarName,
      };

      const query = {
        location_id: carDetails.location.location_id,
        starts: (carDetails.starts
          ? carDetails.starts
          : this.starts
        )?.getTime(),
        ends: (carDetails.ends ? carDetails.ends : this.ends)?.getTime(),
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        cargroup_id: carDetails.cargroup_id,
        subscription: this.isSubscription,
      };
      const referrer = sessionStorage.getItem("referrer");
      if (referrer) {
        query.referrer = referrer;
      }

      if (!cookies.read("authToken")) {
        this.$emit("segment", this.segment.bookNowClickNonLoggedInUser());
        Cart.data.address = this.geoAddress.display_name;
        Cart.data.zipcode = this.geoAddress.postcode;
        if (microsite() || this.isMobile) {
          this.$router.push({
            name: "CarDetailPage",
            params,
            query,
          });
        } else {
          const route = this.$router.resolve({
            name: "CarDetailPage",
            params,
            query: {
              ...query,
              available_add_ons: car.car_data?.old_checkout_params?.available_add_ons
            },
          });
          window.open(route.href, "_blank");
        }
      } else {
        Cart.data.selected_terminal = "";

        this.fetchGeoAddress({
          coords: { lat: this.origin.lat, lng: this.origin.lng },
        })
          .then((res) => {
            if (res && res[0]) {
              Cart.data.address = res[0].display_name;
              Cart.data.zipcode = res[0].postcode;
            }
          })
          .catch((err) => {
            return err;
          });
        Cart.data.isTerminal = false;

        if (Cart.data.hd == 0) {
          if (microsite() || this.isMobile) {
            this.$router.push({
              name: "CarDetailPage",
              params,
              query,
            });
          } else {
            const route = this.$router.resolve({
              name: "CarDetailPage",
              params,
              query: {
                ...query,
                available_add_ons: car.car_data?.old_checkout_params?.available_add_ons
              },
            });
            window.open(route.href, "_blank");
          }
        } else {
          this.fetchGeoAddress({
            coords: { lat: this.origin.lat, lng: this.origin.lng },
          })
            .then((res) => {
              if (res && res[0]) {
                Cart.data.address = res[0].display_name;
                Cart.data.locality = res[0].district;
              }
            })
            .catch((err) => {
              return err;
            });
          this.$router.push({
            name: "CarDetailPage",
            params: {
              city_link,
              country_code: this.$store.getters["App/countryCode"],
            },
          });
        }
      }
    },
    handleFavClick() {
      this.$emit("toast", "Login to mark the car as favourite", "error");
    },
    handleSegment(event, data) {
      if (event === "image_swiped") {
        this.$emit("segment", this.segment.imageSwiped(data));
      }
      if (event == "clickEvent") {
        this.$emit("segment", this.segment.onClickEvent(data));
      }
      if (event == "screenLoaded") {
        this.$emit("segment", this.segment.onPageLoad(data));
      }
    },
    handleBannerClick(page) {
      if (page == "SearchPage") this.handleGetCarClick("marketing_banner");
      else {
        this.$router.push({
          name: page,
          params: {
            city_link: this.userCityLinkName,
            country_code: this.countryCode,
          },
        });
      }
    },

    handleGetCarClick(click_category = "", buttonType = "", car_category = "") {
      this.$emit("segment", this.segment.findCarsClick(click_category));
      //create new search session
      sessionStorage.setItem("search_session_id", uuidv4());
      sessionStorage.setItem("location", JSON.stringify(this.origin));
      this.$store.commit("HomeNew/setCarSelectionToSearchFlow", false);
      let query = {
        lat: this.origin.lat,
        lng: this.origin.lng,
        type: this.product,
        starts: this.starts?.getTime(),
        ends: this.ends?.getTime(),
        car_id: this.selectedCar.search_params["seek.car_id"],
        city_id: this.selectedCar.search_params["seek.city_id"],
        lat_e5: this.selectedCar.search_params["seek.lat_e5"],
        lng_e5: this.selectedCar.search_params["seek.lng_e5"],
        location_id: this.selectedCar.search_params["seek.location_id"],
        cargroup_id: this.selectedCar.cargroup_id,
      };
      const searchRoute = {
        name: "SearchPage",
        query: query,
        params: {
          city_link: this.userCityLinkName,
          country_code: this.countryCode,
        },
      };
      sessionStorage.setItem("searchRoute", JSON.stringify(searchRoute));

      if (buttonType === "ratings") {
        query.ratings = "rated_450";
      } else if (buttonType === "car_type") {
        if (car_category === "ELECTRIC") {
          query.fuel_type = "ELECTRIC";
        } else query.car_type = car_category || "SUV";
      } else if (buttonType === "delivery_type") {
        query.delivery_type = "home";
      } else if (buttonType === "sort_by") {
        query.sort_by = "low_to_high";
      }

      this.$router.push({
        name: "SearchPage",
        query,
        params: {
          city_link: this.userCityLinkName,
          country_code: this.countryCode,
        },
      });
      sessionStorage.setItem("selectedCar", JSON.stringify(this.selectedCar));
    },

    handleTopCityClick(city) {
      this.$emit("segment", this.segment.onTopCityClick(city));
      window.open(`${window.location.origin}/in/${city}`, "_self");
    },

    openHostPage() {
      window.open("https://www.zoomcar.com/in/host", "_blank");
    },

    handleScroll() {
      let newPosition = window.scrollY;
      this.scrollPosition = newPosition;
      this.$emit("segment", this.segment.onScroll(newPosition));
    },

    visibilityObserver() {
      const options = {
        threshold: [1.0],
      };
      // IntersectionObserver callback
      const callback = (entries) => {
        entries.forEach((entry) => {
          if (entry.intersectionRatio === 1.0) {
            this.$emit(
              "segment",
              this.segment.onClickEvent(`${entry.target.id}_section_visible`)
            );
          }
        });
      };
      this.observer = new IntersectionObserver(callback, options);
      const appBanner = document.getElementById(`app-download-banner`);
      const hostBanner = document.getElementById(`host-banner`);
      appBanner && this.observer.observe(appBanner);
      hostBanner && this.observer.observe(hostBanner);
    },

    shortTermClick() {
      this.$emit("segment", this.segment.findCarsClick("short term"));
      //create new search session
      let endDate = new Date(this.starts.getTime());
      endDate.setMinutes(this.starts.getMinutes() + 720);
      this.$store.commit("Search/setDateTime", {
        point: "ends",
        date: endDate.getTime(),
      });
      this.setSubscription(false);
      sessionStorage.setItem("search_session_id", uuidv4());
      sessionStorage.setItem("location", JSON.stringify(this.origin));
      this.$store.commit("HomeNew/setCarSelectionToSearchFlow", false);
      let query = {
        lat: this.origin.lat,
        lng: this.origin.lng,
        type: this.product,
        starts: this.starts?.getTime(),
        ends: endDate.getTime(),
        car_id: this.selectedCar.search_params["seek.car_id"],
        city_id: this.selectedCar.search_params["seek.city_id"],
        lat_e5: this.selectedCar.search_params["seek.lat_e5"],
        lng_e5: this.selectedCar.search_params["seek.lng_e5"],
        location_id: this.selectedCar.search_params["seek.location_id"],
        cargroup_id: this.selectedCar.cargroup_id,
      };
      const searchRoute = {
        name: "SearchPage",
        query: query,
        params: {
          city_link: this.userCityLinkName,
          country_code: this.countryCode,
        },
      };
      sessionStorage.setItem("searchRoute", JSON.stringify(searchRoute));

      this.$router.push({
        name: "SearchPage",
        query,
        params: {
          city_link: this.city||this.userCityLinkName,
          country_code: this.countryCode,
        },
      });
      sessionStorage.setItem("selectedCar", JSON.stringify(this.selectedCar));
    },

    triggerClarityCustomEvent() {
      if (window.clarity && this.isMobile) {
        try {
          let experimentName = ab.getExperimentName();
          let experimentValue = ab.getExperimentValue();
          window.clarity("set", experimentName, `${experimentValue}`);
          window.clarity("event", `${experimentName}-${experimentValue}`);
        } catch (e) {}
      }
    },

    handlePopupClose() {
      this.$emit("segment", this.segment.onClickEvent(`mweb_popup_close`));
      this.showMwebPopup = false;
    },

    logicToShowPipVideo() {
      this.showVideo = false;
      this.timeout = setTimeout(() => {
        this.showVideo = true;
      }, 2000);
    },
    setSchema(data, lat, lng, starts, ends) {
      const defaultParams = {
        city_link: this.userCityLinkName || this.$route.params?.["city_link"],
        country_code: this.$route.params["country_code"]
      }

      const defaultQuery = {
        lat: this.origin.lat || lat,
        lng: this.origin.lng || lng,
        starts: this.starts.getTime() || starts,
        ends: this.ends.getTime() || ends,
      }

      setItemListSchema(data || [], defaultParams, defaultQuery)
    },
  },
  watch: {
    city(newCity) {
      this.fetchCarSuggetions();
      this.fetchBlogs();
    },
    origin(newOrigin) {
      this.fetchCarSuggetions();
      this.setSearchQuery();
    },
    starts() {
      this.fetchCarSuggetions();
      this.setSearchQuery();
    },
    ends() {
      this.fetchCarSuggetions();
      this.setSearchQuery();
    },
  },
};
