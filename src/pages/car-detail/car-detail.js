import { mapGetters, mapActions } from "vuex";
import { loadGoogleApiScript } from "~/helpers/geo.js";
import CarDetailsNew from "~/components/car-details-new";
import CheckoutNew from "../checkout-new";
import PageLoader from "~/components/page-loader";
import { format } from "date-fns/esm";
import LocationShow from "./location-show";
import CalendarCheckout from "~/components/calendar-checkout";
import cookies from "~/helpers/cookies";
import CalendarPopup from "~/components/calendar-popup";



export default {
  name: "page-car-detail",
  components: {
    PageLoader,
    CheckoutNew,
    CarDetailsNew,
    LocationShow,
    CalendarCheckout,
    CalendarPopup
  },
  created() {
  },
  mounted() {

    let city = cookies.read("city")
    if(city) {
      this.fetchAvailabilityCalendar({
        car_id: this.$route.params.car_id,
        city,
        set_search:false,
      }).then((res) => {
        this.$store.commit("HomeNew/setCarSelectionToSearchFlow", true);
      });
    }
    this.initData();
  },
  data() {
    return {
      carDetailLoading: true,
      carDetails: null,
      showPicker: false,
      isCustomFlow: true,
      handleGetCheckoutData:false,
    };
  },
  methods: {
    ...mapActions("CarDetail", ["fetchCarData"]),
    ...mapActions({
      fetchAvailabilityCalendar: "HomeNew/fetchAvailabilityCalendar",}),
    handleShowCalendar(){
      this.showPicker=true;
      this.fetchAvailabilityCalendar({
        car_id: this.$route.params.car_id,
        city: this.carDetails.car_data.city,
        set_search:false,
      }).then((res) => {
        this.$store.commit("HomeNew/setCarSelectionToSearchFlow", true);
      });
    },
    handleDateSubmit(date) {
      let searchData = JSON.parse(sessionStorage.getItem("searchRoute"));
      searchData.query.starts=date.start.getTime();
      searchData.query.ends=date.end.getTime();
      sessionStorage.setItem("searchRoute", JSON.stringify(searchData));
      this.handleGetCheckoutData=true;
      this.showPicker = false;

      this.$store.commit("Search/setDateTime", {
        point: "starts",
        date: date.start,
      });

      this.$store.commit("Search/setDateTime", {
        point: "ends",
        date: date.end,
      });
      const query = { 
        ...this.$route.query, 
        starts: date.start.getTime(), 
        ends: date.end.getTime() 
      };
      this.$router.replace({ query });
      this.$store.state.Cart.updateTripDates(date.start,date.end)

    },
      handleError(error) {
        this.pageDialog = {
          show: true,
          title: "Error",
          msg: error,
          actions: [
            {
              primary: true,
              method: () => (this.pageDialog = {}),
              label: "ok"
            }
          ]
        };
      },

    initData() {
      const carId = this.$route.params.car_id;
      this.fetchCarData(carId)
        .then((res) => {
          this.carDetails = res;
          this.carDetailLoading = false;
        })
        .catch((err) => {
          this.carDetailLoading = false;
        });
    },

    handleBackClick() {
      const previousScreen=sessionStorage.getItem("previous_screen")
      const searchData = JSON.parse(sessionStorage.getItem("searchRoute"));
      this.$router.go(-1)
        

    },
  },
  computed: {
    ...mapGetters({
      carData: "CarDetail/carData",
      isMobile: "App/isMobile",
      favouriteCars: "User/favouriteCars",

    }),

    showCarDetailsNew() {
      return this.$route.name === "CarDetailsPageNew";
    },
  },
  watch: {
    carData() {
      if (this.carData.car_location) {
        loadGoogleApiScript().then(() => {
          // this.showMap();
        });
      }
    },
  },
  filters: {
    format: format,
  },
  destroyed() {
    document.body.style.backgroundColor = "unset";
  },
};
