/* @flow */
import { mapGetters, mapActions, mapState } from "vuex";
import { format } from "date-fns/esm";
import SearchTopNav from "../../components/search-top-nav";
import SearchLocationCalendarNew from "../../components/search-location-calendar-new";
import segment from "./analytics/segment";
import cookies from "~/helpers/cookies";
import CarItemSearch from "../../components/car-item-search";
import SearchFilters from "./search-filters";
import IconList from "~/components/icon-list";
import { microsite } from "../../helpers/microsite/utils";
import { bajajFinservSession } from "../../helpers/microsite/bajajfinserv";
import { airindiaSession } from "../../helpers/microsite/airindia";
import { airindiaexpressSession } from "../../helpers/microsite/airindiaexpress";
import { yonosbiSession } from "../../helpers/microsite/yonosbi";
import {
  AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY,
  BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
  DISPLAY_MICROSITE_APPLY_OFFER_POPUP,
  AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
  YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY,
} from "../../helpers/microsite/constants";
import CustomSelect from "../../components/custom-select";
import SearchCar from "../../components/search-car";
import SearchCarMweb from "../../components/search-car-mweb";
import SortMweb from "../../components/sort-mweb";
import SubscriptionPopup from "~/components/subscription-popup";
export default {
  name: "page-search",
  components: {
    CustomSelect,
    SearchTopNav,
    // SearchLocationCalendar,
    SortMweb,
    SearchLocationCalendarNew,
    CarItemSearch,
    IconList,
    SearchFilters,
    SearchCar,
    SearchCarMweb,
    SubscriptionPopup
  },

  data() {
    return {
      pageLoading: false,
      filterLoading: false,
      response: {},
      showFilter: false,
      hasScrolled: false,
      carItemSearch: null,
      checkedFilters: [],
      filters: [],
      bracket: "no_fuel",
      flexName: "",
      noFuelEnabled: false,
      showErrorMsg: false,
      selectedFilters: {},
      showNotifyMePopup: false,
      selectedDropLocation: null,
      sliderValue: {},
      timeOutId: undefined,
      DEBOUNCE_DELAY: 300,
      searchedItem: "",
      availableTextItems: [],
      filteredTextItems: [],
      selectedInputItems: [],
      hideSpecificSearchIds: [],
      isFilterCollapsed: true,
      isCollapsed: false,
      categories: [],
      activeCategory: null,
      searchCategoryQueryParam: "",
      geoAddress: {},
      responseV8: {
        cars: [],
      },
      displayedCars: [],
      filtersNew: [],
      apiCall: false,
      sortOption: null,
      showSubscriptionPopup: false,
    };
  },

  beforeCreate() {
    const { city_link } = this.$route.params;
    const userCityName = this.$store.getters["User/userCityLinkName"];
    const routeBack = this.$store.getters["App/routeBack"];
    if (!city_link) {
      this.$router.replace({
        name: "SearchPage",
        params: {
          city_link: userCityName,
          country_code: this.$store.getters["App/countryCode"],
        },
        query: this.$route.query,
      });
    } else if (userCityName != city_link) {
      if (routeBack && userCityName) {
        this.$router.replace({
          name: "SearchPage",
          params: {
            city_link: userCityName,
            country_code: this.$store.getters["App/countryCode"],
          },
          query: this.$route.query,
        });
      } else {
        this.$store.commit("User/setUserCityLinkName", city_link);
      }
    }
  },

  created() {
    // facebook pixel integration on search page
    /*global fbq*/
    this.fetchFavIds().catch(() => {});
    window.fbq ? fbq("track", "Search") : null;
    this.pageLoading = true;
    this.filterLoading = true;
    const query = this.query;
    sessionStorage.setItem("searchQuery", JSON.stringify(query));
    setTimeout(
      () =>
        this.$emit(
          "segment",
          this.segment.searchPageLoaded("Search", +query.starts, +query.ends)
        ),
      500
    );

    if (query.bracket) {
      this.bracket = query.bracket;
    }

    this.getCurrentCity().then((currentCity) => {
      this.$store.commit("User/setCityData", currentCity);
    });

    if (!this.$store.getters["Search/origin"].name) {
      const lat = +query.lat;
      const lng = +query.lng;

      try {
        this.$store.commit("Search/setProduct", query.type);
      } catch (e) {
        this.pageLoading = false;
        this.response = {
          status: 0,
          msg: e.message,
        };
        return e;
      }

      this.$store.commit("Search/setDateTime", {
        point: "starts",
        // date: new Date(query.starts.replace(/-/g, "/"))
        date: parseInt(query.starts),
      });

      this.$store.commit("Search/setDateTime", {
        point: "ends",
        // date: new Date(query.ends.replace(/-/g, "/"))
        date: parseInt(query.ends),
      });

      this.$store.commit("Search/setLatLng", {
        point: "origin",
        lat,
        lng,
      });

      this.fetchGeoAddress({
        coords: { lat: lat, lng: lng },
      })
        .then((res) => {
          if (res && res[0]) {
            this.geoAddress = res[0];
            this.$store.commit("Search/setLocationName", {
              point: "origin",
              name: res[0].display_name,
            });
          }
        })
        .catch((err) => {
          return err;
        });
    } else {
      const { lat, lng } = this.$route.query;
      this.fetchGeoAddress({
        coords: { lat: lat, lng: lng },
      })
        .then((res) => {
          if (res && res[0]) {
            this.geoAddress = res[0];
          }
        })
        .catch((err) => {
          return err;
        });

      this.syncUrlWithSearch();
    }

    this.$store.commit("Search/setPagination", {
      has_more: false,
      cache_key: null,
      index: null,
    });
    this.pullV8Cars(false, true);
    /**
     * Handle filter visibility
     */
    this.showFilter = window.location.hash == "#filter";
  },

  mounted() {
    window.scrollTo(0, 0);
    window.addEventListener('scroll', this.handleScroll);
    const carItemSearch = document.querySelector(".car-search-list-wrapper");
    if (carItemSearch) {
      carItemSearch.addEventListener('scroll', this.handleScroll);
      this.carItemSearch = carItemSearch; 
    }
    if (!this.isSubscription && this.query.subscription === "true") {
      this.setSubscription(true);
    }

  },

  computed: {
    ...mapState("Search", ["distance"]),
    ...mapState("Location", ["isCitiesLoaded"]),
    ...mapState("App", ["locale"]),
    ...mapGetters({
      isMobile: "App/isMobile",
      country: "App/country",
      online: "App/online",
      user: "User/user",
      authToken: "User/authToken",
      userCityName: "User/userCityName",
      userCityLinkName: "User/userCityLinkName",
      userCity: "User/userCity",
      productTitle: "Search/productTitle",
      products: "Search/products",
      product: "Search/product",
      starts: "Search/starts",
      ends: "Search/ends",
      origin: "Search/origin",
      destination: "Search/destination",
      isCitiesLoading: "Location/isCitiesLoading",
      cities: "Location/cities",
      locale: "App/locale",
      pagination: "Search/pagination",
      isSubscription: "Search/isSubscription",
    }),

    segment() {
      return segment.call(this);
    },

    query() {
      return this.$route.query;
    },
    isTab() {
      return this.$store.state.App.windowWidth <= 900;
    },
    cardsV8() {
      const { responseV8 } = this;
      if (responseV8.empty_response) return [];
      let { lat, lng } = this.$route.query;
      let data = this.displayedCars?.map((item) => {
        return {
          car_data: {
            ...item,
            location_new: item.location,
            image_urls: item.images,
            pricing: {
              payable_amount: item.price_line_1.normal_text,
              fare_header: item.price_line_2.header,
            },
            add_ons: item.old_checkout_params.available_add_ons.map((item) => ({
              label: item,
              id: item,
            })),
            location: {
              prime_location_id: item.old_checkout_params.prime_location_id,
              location_id: item.old_checkout_params.location_id,
              lat,
              lng,
              start: this.starts,
              ends: this.ends,
              // distance: 1.2590015012609357,
              // text: "1.9 km away",
              hd_ids: [],
              terminal_ids: [],
              // zoom_air_unvailable_reason: "LEAD_TIME",
            },
          },
        };
      });
      return data;
    },

    range() {
      return this.sliderValue;
    },

    isFilterAcive() {
      return this.filtersNew.length !== 0;
    },

    filterCount() {
      if (Array.isArray(this.quickFilters) && this.quickFilters.length > 0) {
        return this.quickFilters.filter((filter) => filter.selected === true)
          .length;
      }
      return 0;
    },

    sortFilters() {
      const sortOptions =
        this.responseV8.prominent_nav_filters?.sort_by_nav_item;
      return sortOptions;
    },
    quickFilters() {
      const quickFilters = this.responseV8.nav_filters?.items;
      const priceFilter = this.filtersNew.find(
        (filter) => filter.id == "price"
      );
      if (priceFilter) {
        const min = priceFilter.selected_min || priceFilter.min;
        const max = priceFilter.selected_max || priceFilter.max;

        quickFilters.unshift({
          id: "price",
          text: `${priceFilter.prefix || ""}${priceFilter.value?.[0]} - ${priceFilter.value?.[1]}`,
          icon: "currency_rupee",
          icon_img: null,
          enabled: true,
          selected: true,
          type: "SINGLE_SELECT",
          selected_option: null,
          options: null,
          value: "price",
        });
      }
      return quickFilters;
    },
  },

  watch: {
    $route() {
      this.showFilter = window.location.hash == "#filter";
    },

    bracket() {
      this.syncUrlWithSearch();
    },

    flexName(newVal, oldVal) {
      if (oldVal && newVal != oldVal) {
        this.syncUrlWithSearch();
      }
    },

    checkedFilters: {
      handler: function () {
        if (!this.isMobile) {
          this.syncUrlWithSearch();
          this.pullV8Cars(true);
        }
      },
    },

    filtersNew() {
      if (this.apiCall) {
        this.displayedCars = [];
        this.$store.commit("Search/setPagination", {
          has_more: false,
          cache_key: null,
          index: null,
        });
        this.pullV8Cars(true);
      }
      this.updateUrlParams();
    },

    sortOption(newVal, oldVal) {
      if (newVal?.id != oldVal?.id || oldVal == null) {
        this.displayedCars = [];
        this.$store.commit("Search/setPagination", {
          has_more: false,
          cache_key: null,
          index: null,
        });
        this.pullV8Cars(true);
      }
    },
  },

  methods: {
    ...mapActions({
      fetchCities: "Location/fetchCities",
      fetchCars: "Search/fetchCars",
      fetchGeoAddress: "Search/fetchGeoAddress",
      fetchV8Cars: "Search/fetchV8Cars",
      fetchFavIds: "User/fetchFavIds",
      setSubscription: "Search/setSubscription",
    }),
    updateUrlParams() {
      const filtersall = this.getFilters(false);
      const { lat, lng, type, cargroup_id, starts, ends } = this.$route.query;
      const allParams = {
        lat,
        lng,
        type,
        cargroup_id,
        starts,
        ends,
        ...filtersall,
      };
      const url = new URL(window.location);
      const queryParams = Object.keys(allParams).reduce((acc, key) => {
        const value = allParams[key];
        if (Array.isArray(value)) {
          acc[key] = value.join(",");
        } else if (value !== undefined && value !== null) {
          acc[key] = value.toString();
        }
        return acc;
      }, {});
      Object.keys(queryParams).forEach((key) => {
        url.searchParams.set(key, queryParams[key]);
      });
      this.$router.replace({ query: { ...queryParams } });
    },
    handleScroll() {
      const windowScrolled = window.scrollY > 50;
      let carItemScrolled = false;
      if (this.carItemSearch) {
        carItemScrolled = this.carItemSearch.scrollTop > 100;
      }
      this.hasScrolled = windowScrolled || carItemScrolled;
    },

    handleSortSelect(data) {
      this.sortOption = data;
    },
    handleFavClick() {
      this.$emit("toast", "Login to mark the car as favourite", "error");
    },
    handleFilterClick(filter) {
      this.$refs.searchFilters.handleHeaderClick(filter, true);
    },

    quickFilterCross(data) {
      let filter = this.filtersNew.find((item) => item.id === data.id);
      if (filter) {
        this.handleFilterCross(filter);
      }
    },
    handleBackClick() {
      const previousScreen = sessionStorage.getItem("previous_screen");
      if (
        !previousScreen ||
        previousScreen == "HomePage" ||
        previousScreen == "LandingPageNew"
      ) {
        this.$router.go(-1);
      }else {
        this.$router.push({
          name: "HomePage",
          params: {
            city_link: this.userCityLinkName,
          }
        });
      }
    },
    handleFilterCross(filter) {
      this.apiCall = true;
      if (filter.type === "checkbox") {
        filter.selected = false;
      }
      this.filtersNew = this.filtersNew.filter(
        (val) => val.value !== filter.value
      );

      //url update
      const params = new URLSearchParams(window.location.search);
      let values = params.get(filter.id).split(",");
      values = values.filter((value) => value !== filter.value);

      if (
        filter.id == "price" ||
        filter.id == "model_year" ||
        filter.id == "distance_radius"
      ) {
        params.delete(filter.id);
      } else {
        if (values.length > 0) params.set(filter.id, values.join(","));
        else params.delete(filter.id);
      }

      const newUrl = `${window.location.pathname}?${params.toString()}`;
      window.history.pushState({}, "", newUrl);

      //filters comp update
      this.$refs.searchFilters.handleFilterCross(filter, this.filtersNew);
    },

    handleFiltersReset() {
      this.apiCall = true;
      this.removeQueryParam();
      this.filtersNew = [];
      this.$store.commit("Search/setPagination", {
        has_more: false,
        cache_key: null,
        index: null,
      });
      this.$refs.searchFilters.setFilters();
    },

    removeQueryParam() {
      const { lat, lng, starts, ends, type } = this.$route.query;

      this.$router.replace({
        query: {
          lat,
          lng,
          starts,
          ends,
          type,
        },
      });
    },

    handleFiltersChange(data, apiCall = false) {
      if (apiCall) {
        this.apiCall = true;
      }

      // if no filters are present
      if (!this.filtersNew.length) {
        this.filtersNew = [...this.filtersNew, data];
      } else {
        // filters present
        const isFilterPresent = this.filtersNew.some(
          (filter) => filter.value === data.value
        );
        if (data.type === "checkbox") {
          if (isFilterPresent) {
            this.filtersNew = this.filtersNew.filter(
              (val) => val.value !== data.value
            );
          } else {
            this.filtersNew = [...this.filtersNew, data];
            this.scrollToLeft();
          }
        } else {
          const isFilterPresent = this.filtersNew.some(
            (filter) => filter.id === data.id
          );

          if (isFilterPresent) {
            this.filtersNew = this.filtersNew.map((val) => {
              if (val.id === data.id) {
                val = data;
              }
              return val;
            });
          } else {
            this.filtersNew = [...this.filtersNew, data];
            this.scrollToLeft();
          }
        }
      }
    },

    setApiCall() {
      this.apiCall = true;
    },
    scrollToLeft() {
      if (this.$refs.filterTabs) {
        let targetLastElement = this.$refs.filterTabs.getElementsByClassName(
          "search-filter-tabs-tab"
        );
        if (targetLastElement.length > 3) {
          targetLastElement = targetLastElement[targetLastElement.length - 1];

          setTimeout(() => {
            this.$refs.filterTabs.scrollLeft =
              this.$refs.filterTabs.scrollWidth;
          }, 300);
        }
      }
    },
    getCurrentCity() {
      return new Promise((resolve) => {
        if (this.userCityName) return resolve(this.userCity);

        if (!this.isCitiesLoading && !this.isCitiesLoaded) {
          this.fetchCities().then(() =>
            resolve(
              this.cities(this.country).find(
                (city) => city.link_name == this.userCityLinkName
              )
            )
          );
        } else {
          resolve(
            this.cities(this.country) &&
              this.cities(this.country).length &&
              this.cities(this.country).find(
                (city) => city.link_name == this.userCityLinkName
              )
          );
        }
      });
    },

    handleCarSelection(carDetails, car) {
      const { Cart } = this.$store.state;

      //first_call is being set as true
      //the idea is that the checkout api is being called for first time when the user checks the checkout summary
      //for the case of returning back to search page from checkout summary page and opening a new car's checkout summary
      //    - it does not trigger a reload, so first_call is false in that case for a new car
      //    - this causes the cta_enabled property as false resulting in checkout button as disabled

      Cart.first_call = true;

      const { city_link } = this.$route.params;
      const pricings = car.pricing || car.pricings;
      const priceIndex = pricings
        ? pricings
            .map((p, i) => ({ ...p, index: i }))
            .filter((p) => p.id == carDetails.price_id)
            .map((p) => p.index)[0]
        : null;

      const addOns = car.car_data.add_ons
        ? car.car_data?.add_ons?.map((item) => item.id)
        : [];

      Cart.data = {
        car: car.car_data.name,
        brand: car.car_data.brand,
        car_id: carDetails.car_id,
        cargroup_id: carDetails.cargroup_id,
        car_price: carDetails.car_price,
        city: city_link,
        starts: carDetails.starts ? carDetails.starts : this.starts,
        ends: carDetails.ends ? carDetails.ends : this.ends,
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        search_experiments: this.response.experiments,
        hd: 0,
        offer_id: carDetails.offer_id ? carDetails.offer_id : null,
        radius: carDetails.radius,
        exactLocation: "",
        terminal_id: carDetails.terminal_id,
        hd_location_ids: "",
        user_city: "",
        user_address_id: "",
        flexi_id: { "0": 5, "1": 10, "2": 15 }[priceIndex],
        last_selected_option: {},
        offer_params: null,
        promo: "",
        section_params: [],
        deal: null,
        seater: carDetails.seater,
        transmission: carDetails.transmission,
        flex_name: carDetails.flex_name,
        hd_unavailable_reason: carDetails.location.hd_unavailable_reason,
        add_ons: addOns,
      };

      if (carDetails.offer_id) {
        Cart.data = {
          offer_params: {
            offer_id: carDetails.offer_id,
          },
        };
      }

      Cart.data = {
        location_id: carDetails.location_id || carDetails.starting_location_id,
        location_name: carDetails.locName,
        type: "round_trip",
        hd_location_ids:
          (carDetails.location.hd_ids.length && carDetails.location.hd_ids) ||
          null,
        hd: (carDetails.location.hd_ids.length && 1) || 0,
        // location_name: encodeURIComponent(this.origin.name),
        lat: this.origin.lat,
        lng: this.origin.lng,
        user_address_id: 0,
        edit: false,

        //reset HD data on new car selection
        address_id: "",
        terminal_id: "",
        section_params: [],
      };

      this.$emit("segment", this.segment.bookNowClick(Cart.data));

      if (microsite()) {
        if (bajajFinservSession()) {
          sessionStorage.setItem(
            BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaSession()) {
          sessionStorage.setItem(AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaexpressSession()) {
          sessionStorage.setItem(
            AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (yonosbiSession()) {
          sessionStorage.setItem(YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        }
      }

      const params = {
        city_link,
        country_code: this.$store.getters["App/countryCode"],
        car_id: Cart.data.car_id,
        car_name: Cart.routeCarName,
        
      };

      const query = {
        location_id: carDetails.location.location_id,
        starts: (carDetails.starts
          ? carDetails.starts
          : this.starts
        )?.getTime(),
        ends: (carDetails.ends ? carDetails.ends : this.ends)?.getTime(),
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        cargroup_id: carDetails.cargroup_id,
        subscription: this.isSubscription,
      };
      const referrer = sessionStorage.getItem("referrer");
      if (referrer) {
        query.referrer = referrer;
      }

      if (!cookies.read("authToken")) {
        this.$emit("segment", this.segment.bookNowClickNonLoggedInUser());
        Cart.data.address = this.geoAddress.display_name;
        Cart.data.zipcode = this.geoAddress.postcode;
        if (microsite() || this.isMobile) {
          this.$router.push({
            name: "CarDetailPage",
            params,
            query,
          });
        } else {
          const route = this.$router.resolve({
            name: "CarDetailPage",
            params,
            query: {
              ...query,
              available_add_ons: car.car_data?.old_checkout_params?.available_add_ons
            },
          });
          window.open(route.href, "_blank");
        }
      } else {
        Cart.data.selected_terminal = "";

        this.fetchGeoAddress({
          coords: { lat: this.origin.lat, lng: this.origin.lng },
        })
          .then((res) => {
            if (res && res[0]) {
              Cart.data.address = res[0].display_name;
              Cart.data.zipcode = res[0].postcode;
            }
          })
          .catch((err) => {
            return err;
          });
        Cart.data.isTerminal = false;

        if (Cart.data.hd == 0) {
          if (microsite() || this.isMobile) {
            this.$router.push({
              name: "CarDetailPage",
              params,
              query,
            });
          } else {
            const route = this.$router.resolve({
              name: "CarDetailPage",
              params,
              query: {
                ...query,
                available_add_ons: car.car_data?.old_checkout_params?.available_add_ons
              },
            });
            window.open(route.href, "_blank");
          }
        } else {
          this.fetchGeoAddress({
            coords: { lat: this.origin.lat, lng: this.origin.lng },
          })
            .then((res) => {
              if (res && res[0]) {
                Cart.data.address = res[0].display_name;
                Cart.data.locality = res[0].district;
              }
            })
            .catch((err) => {
              return err;
            });
          this.$router.push({
            name: "CarDetailPage",
            params: {
              city_link,
              country_code: this.$store.getters["App/countryCode"],
            },
          });
        }
      }
    },

    syncUrlWithSearch() {
      const { type, starts, ends, lat, lng } = this.$route.query;
      this.$store.commit("Search/setProduct", type);
      this.$store.commit("Search/setDateTime", {
        point: "starts",
        date: parseInt(starts),
      });
      this.$store.commit("Search/setDateTime", {
        point: "ends",
        date: parseInt(ends),
      });
      this.$store.commit("Search/setLatLng", { lat, lng, point: "origin" });

      !this.starts
        ? this.$store.commit("Search/setDateTime", {
            point: "starts",
            date: parseInt(starts),
          })
        : null;

      !this.ends
        ? this.$store.commit("Search/setDateTime", {
            point: "ends",
            date: parseInt(ends),
          })
        : null;
    },

    getFilters(isFirstCall) {
      const filters = {};
      this.filtersNew.forEach((filter) => {
        if (!filters[filter.id]) filters[filter.id] = [];
        if (filter.type === "minmaxSlider") {
          filters[filter.id] = filter.value;
        } else filters[filter.id].push(filter.value);
      });

      if (isFirstCall) {
        let queryFilters = this.getFiltersFromQuery();
        queryFilters.forEach((filter) => {
          if (!filters[filter.id]) filters[filter.id] = [];
          if (filter.type === "minmaxSlider") {
            filters[filter.id] = filter.value;
          } else filters[filter.id].push(filter.value);
        });
      }

      if (this.sortOption) {
        filters[this.sortFilters.id] = this.sortOption.id;
      }
      return filters;
    },

    navFiltersClick(data) {
      if (data.selected) {
        this.quickFilterCross(data);
      } else this.$refs.searchFilters.navFiltersClick(data);
    },

    getFiltersFromQuery() {
      const { query } = this.$route;
      const excludeKeys = [
        "lat",
        "lng",
        "type",
        "cargroup_id",
        "starts",
        "ends",
      ];

      return Object.keys(query)
        .filter((key) => !excludeKeys.includes(key))
        .map((key) => {
          const value = query[key];

          const processedValue =
            typeof value === "string" && value.includes(",")
              ? value.split(",")
              : value;

          if (key == "undefined") {
            return {
              id: "ratings",
              value: processedValue,
            };
          }
          return {
            id: key,
            value: processedValue,
          };
        });
    },

    pullV8Cars(scroll, isFirstCall = false) {
      if (scroll) {
        let carListElement = document.querySelector(".car-search-list-wrapper");
        if (carListElement) {
          carListElement.scrollTop = 0;
        }
        window.scrollTo({ top: 0, bottom: 0, behavior: "smooth" });
      }

      this.filterLoading = true;
      this.fetchV8Cars({
        city: this.userCityLinkName,
        selectedFilters: this.getFilters(isFirstCall),
      })
        .then((res) => {
          this.responseV8 = res.data;
          this.pageLoading = false;
          this.filterLoading = false;
          if (res.data.empty_response) {
            this.showErrorMsg = true;
          } else {
            this.showErrorMsg = false;
            this.setCityId();
            this.$store.commit("Search/setPagination", res.data.pagination);
            this.displayedCars = [...res.data.cars];

            if (isFirstCall) {
              let qValue = this.$route.query?.sort_by || "";
              let options = this.sortFilters?.options || [];
              if (options.length > 0) {
                if (qValue) {
                  this.sortOption = options.find((item) => item.id === qValue);
                }
              }
            }

            this.filters = res.data?.filter_page?.content || [];
            for (let i = 0; i < this.filters.length; i++) {
              if (this.filters[i].hide_for_specific_search) {
                this.hideSpecificSearchIds.push(this.filters[i].id);
              }
              if (
                this.filters[i].id in this.selectedFilters &&
                ["MULTI_SELECT", "SINGLE_SELECT"].includes(this.filters[i].type)
              ) {
                this.selectedFilters[this.filters[i].id] = [];
              }
              for (let j = 0; j < this.filters[i].filter_items.length; j++) {
                if (this.filters[i].type === "TEXT_INPUT") {
                  this.availableTextItems =
                    this.filters[i].filter_items[j].metadata.search_data.items;
                  this.filteredTextItems = this.availableTextItems;
                  this.selectedInputItems = this.filters[i].filter_items[
                    j
                  ].metadata.search_data.items.filter(
                    (item) => item.is_selected
                  );
                } else if (this.filters[i].type === "SLIDER") {
                  this.sliderValue[this.filters[i].id] = {
                    val: this.filters[i].filter_items[j].metadata.slider.value,
                    max: this.filters[i].filter_items[j].metadata.slider.max,
                    min: this.filters[i].filter_items[j].metadata.slider.min,
                  };
                } else {
                  switch (this.filters[i].id) {
                    case "sort_by": {
                      switch (this.$route.params.country_code) {
                        case "in": {
                          if (
                            this.filters[i].filter_items[j].icon ===
                            "CHEAP_RUPEE_2"
                          ) {
                            this.filters[i].filter_items[j].icon =
                              "pricey_rupee";
                          }
                          break;
                        }
                      }
                      break;
                    }
                    case "car_type": {
                      this.filters[i].filter_items[j].icon =
                        "car_type_" + this.filters[i].filter_items[j].icon;
                      break;
                    }
                    case "seater": {
                      this.filters[i].filter_items[j].icon =
                        "seats_" + this.filters[i].filter_items[j].id;
                      break;
                    }
                  }
                  if (this.filters[i].filter_items[j].is_selected) {
                    if (this.filters[i].id in this.selectedFilters) {
                      this.selectedFilters[this.filters[i].id].push(
                        this.filters[i].filter_items[j].id
                      );
                    } else {
                      this.selectedFilters[this.filters[i].id] = [
                        this.filters[i].filter_items[j].id,
                      ];
                    }
                  }
                }
              }
            }
          }
        })
        .catch(() => {
          this.pageLoading = false;
          this.filterLoading = false;
          this.showErrorMsg = true;
        })
        .finally(() => {});
    },

    setCityId() {
      const cityInfo = this.cities(this.country)?.find(
        (city) => city.city_link_name === this.userCityLinkName
      );
      this.$store.commit("Search/setCityId", cityInfo?.city_id);
    },

    handleSegment(event, data) {
      if (event === "image_swiped") {
        this.$emit("segment", this.segment.imageSwiped(data));
      }
      if (event === "sort_filter") {
        this.$emit("segment", this.segment.sortFilterClicked(data));
      }
      if (event == "clickEvent") {
        this.$emit("segment", this.segment.clickEvent(data));
      }
      if (event == "screenLoaded") {
        this.$emit("segment", this.segment.screenLoaded(data));
      }
      if (event == "notify_me_submit" || event == "notify_me_close") {
        this.$emit("segment", this.segment.notifyMeSubmit(event, data));
      }
    },

    debounce(delay, cb) {
      if (this.timeOutId) {
        clearTimeout(this.timeOutId);
      }
      this.timeOutId = setTimeout(() => {
        cb();
      }, delay);
    },

    handleAddInputItemFilter(selectedObj, id) {
      this.displayedCars = [];
      let selectedIds = [];
      this.searchedItem = "";
      this.selectedInputItems.push(selectedObj);
      this.filteredTextItems = this.filteredTextItems.filter(
        (itemObj) => itemObj["id"] !== selectedObj.id
      );
      for (let i = 0; i < this.selectedInputItems.length; i++) {
        selectedIds.push(this.selectedInputItems[i].id);
      }
      this.selectedFilters = {
        ...this.selectedFilters,
        [id]: selectedIds,
      };
      if (!isMobile()) {
        this.debounce(this.DEBOUNCE_DELAY, () => {
          this.filterLoading = true;
          this.$store.commit("Search/setPagination", {
            has_more: false,
            cache_key: null,
            index: null,
          });
          this.pullV8Cars(true);
        });
      }
    },

    handleRemoveInputItemFilter(obj, id) {
      this.displayedCars = [];
      let selectedIds = [];
      this.selectedInputItems = this.selectedInputItems.filter(
        (itemObj) => itemObj["id"] !== obj.id
      );
      this.filteredTextItems.push(obj);
      for (let i = 0; i < this.selectedInputItems.length; i++) {
        selectedIds.push(this.selectedInputItems[i].id);
      }
      if (selectedIds.length > 0) {
        this.selectedFilters = {
          ...this.selectedFilters,
          [id]: selectedIds,
        };
      } else {
        delete this.selectedFilters[id];
      }
      if (!isMobile()) {
        this.debounce(this.DEBOUNCE_DELAY, () => {
          this.filterLoading = true;
          this.$store.commit("Search/setPagination", {
            has_more: false,
            cache_key: null,
            index: null,
          });
          this.pullV8Cars(true);
        });
      }
    },

    toggleFilterContainer() {
      this.isFilterCollapsed = !this.isFilterCollapsed;
    },

    handleCategorySelection(item) {
      this.$emit("segment", this.segment.categoryClicked(item.id, item.text));
      //different category selected, reset page state
      this.displayedCars = [];
      this.$store.commit("Search/setPagination", {
        has_more: false,
        cache_key: null,
        index: null,
      });

      this.activeCategory = item;
      this.pullV8Cars(true);
    },

    lastCar(index) {
      return index === this.cardsV8.length - 1;
    },

    loadNextCars() {
      if (this.pagination.has_more) {
        this.fetchV8Cars({
          city: this.userCityLinkName,
          selectedFilters: this.getFilters(),
        }).then((res) => {
          if (res.data.empty_response) {
          } else {
            this.displayedCars = [...this.displayedCars, ...res.data.cars];
          }
          this.$store.commit("Search/setPagination", res.data.pagination);
        });
      }
    },

    handleSearchClick() {
      this.$store.commit("Search/setPagination", {
        has_more: false,
        cache_key: null,
        index: null,
      });
      this.displayedCars = [];
      this.pullV8Cars(true);
    },
  },

  filters: {
    dateFormat(value, Dtformat = "MMM YYYY") {
      return format(value, Dtformat);
    },

    kFormatter(num) {
      return Math.abs(num) > 999
        ? Math.sign(num) * (Math.abs(num) / 1000).toFixed(2) + "k"
        : Math.sign(num) * Math.abs(num);
    },
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    if (this.carItemSearch) {
      this.carItemSearch.removeEventListener('scroll', this.handleScroll);
    }
  },

};
