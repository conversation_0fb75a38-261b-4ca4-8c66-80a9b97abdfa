.page-search-container {
  background-color: $background-color-inverse;
  width: 100%;
  height: 100dvh;
  .car-search-list-wrapper {
    overflow-y: auto;
    width: 100%;
  }
  .fade-banner-enter-active,
  .fade-banner-leave-active {
    transition: opacity 0.5s, max-height 0.5s;
  }
  .fade-banner-enter,
  .fade-banner-leave-to {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
  }
  .fade-banner-enter-to,
  .fade-banner-leave {
    opacity: 1;
    max-height: 200px; /* Adjust to your banner's height */
  }
  .header_new .desktop_header_new-main-left {
    #logo a img {
      height: 30px;
      @media screen and (max-width: $tab-max-width) {
        height: $padding20;
      }
    }
    .hamburger {
      i {
        font-size: $padding24;
      }
    }
  }
  .dot {
    position: absolute;
    font-size: 12px;
    width: 83px;
    height: 32px;
    background-color: white;
    border: $border-default;
    border-radius: 18px;
    will-change: transform;
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2),
      0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 2px 2px 0 rgba(0, 0, 0, 0.14);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.page-search {
  height: calc(100vh - 81px);
  display: grid;
  grid-template-columns: auto auto;

  @include respond-to("medium") {
    background-color: $background-color-inverse;
    display: flex;
    width: 100%;
    margin: auto;
    margin-top: $padding16;
  }
  .page-search-error {
    width: calc(100% - #{$filterWidth});
    margin-left: auto;
    margin-right: unset;

    .car-search-list {
      &-filters {
        &-button {
          @include body1;
          display: flex;
          padding: $padding04 $padding08;
          justify-content: center;
          align-items: center;
          gap: $padding08;
          border-radius: $padding08;
          border: $border-default;
          cursor: pointer;
          background-color: $background-color-inverse;
          white-space: nowrap;
          @media screen and (max-width: $mobile-max-width) {
            @include caption;
            gap: $padding04;
          }
          &-active {
            background-color: $background-color-success;
            border-color: $label-color-success;
            color: $label-color-success;
          }
          &-cross {
            position: relative;
            top: -75%;
            right: -10%;
            @media screen and (max-width: $mobile-max-width) {
              right: -15%;
            }
            i {
              color: $label-color-inverse;
              background-color: $background-color-successDark;
              border-radius: 50%;
              padding: $padding02;
            }
          }
        }
      }
      &-filters-container {
        display: flex;
        gap: $padding08;
        width: 100%;
        flex-wrap: wrap;
        @media screen and (max-width: $mobile-max-width) {
          width: calc(100vw - 92px);
          overflow: auto;
          flex-wrap: nowrap;
          padding: $padding08 0;
        }
      }
      &-banner {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        &-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          align-self: stretch;
          &-title {
            @include h2;
            line-height: $padding36;
            color: $label-color-success;
            text-align: center;
          }
          &-subtitle {
            @include body2;
            text-align: center;
          }
        }

        @media screen and (max-width: $mobile-max-width) {
          display: none;
        }
      }
      &-container-sort {
        display: flex;
        justify-content: space-between;
        gap: $padding12;
        width: 100%;
        gap: $padding12;
        width: 100%;
        margin-bottom: $padding08;
      }
    }
    @media screen and (max-width: $tab-max-width) {
      width: 100%;
    }
    .car-search-list-banner {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      align-self: stretch;


      &-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        &-title {
          @include h2;
          line-height: $padding36;
          color: $label-color-success;
          text-align: center;
        }
        &-subtitle {
          @include body2;
          text-align: center;
        }
        &__subscription {
          height: 90px;
          width: 100%;
          display: flex;
          align-items: center;
          position: relative;
          border-radius: 6px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          &--content {
            width: 100%;
            height: 100%;
            padding: $padding12;
            position: absolute;
            top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }

      @media screen and (max-width: $mobile-max-width) {
        display: none;
      }
    }
    @media screen and (max-width: $tab-max-width) {
      width: 100%;
    }
    @media screen and (max-width: 600px) {
      width: 100%;
    }
  }
  .page-search-right {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    &-container {
      padding: 0 $padding16 $padding16;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: $padding16;
      width: 100%;
      box-shadow: 0px 2px 2px 0px rgba(221, 221, 221, 0.5);
      z-index: 3;

      &__mweb-subscription-banner {
        align-self: center;
        width: 100%;
        object-fit: contain;
        cursor: pointer;
      }

      @media screen and (max-width: $tab-max-width) {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: $padding08;
        align-self: stretch;
        padding: $padding16;
        position: sticky;
        top: 64px;
        background-color: white;
        .car-location {
          background-color: $background-color-inverse;
          display: flex;
          align-items: center;
          border-radius: 8px;
          border: $border-default;
          padding: $padding08;
          width: 100%;
          @media screen and (min-width: $tab-max-width) {
            display: none;
          }
          &--back {
            cursor: pointer;
            i {
              font-size: 24px;
              color: $label-color-primary;
            }
          }
        }
        .car-search-list-container-sort {
          display: flex;
          justify-content: space-between;
          gap: $padding12;
          width: 100%;
        }
      }
    }
    .search-filter-container {
      position: relative;
      .search-filter-tabs {
        color: $label-color-success;

        &-container {
          width: calc(100% - 110px);
          display: flex;
          gap: $padding08;
          flex-wrap: wrap;
        }
        &-tab {
          border-radius: 4px;
          border: $border-success;
          // padding: 4px;
          padding-left: 6px;
          padding-bottom: 4px;
          &-container {
            width: max-content;
            display: flex;

            &-left {
              &-label-upper {
                font-size: 12px;
              }
              &-label-lower {
                font-size: 16px;
                font-weight: 600;
              }
            }
            &-right {
              display: flex;
              align-items: center;
              padding-left: 10px;
              padding-right: 10px;
              i {
                cursor: pointer;
                font-size: 24px;
              }
            }
          }
        }
      }
      .search-filter-reset {
        position: absolute;
        right: $padding00;
        top: $padding00;
        padding: $padding12;
        background: $background-color-successDark;
        @include title1;
        color: $label-color-inverse;
        border-radius: $padding04;
        cursor: pointer;
        border: none;
        outline: none;
      }
    }
    .car-location {
      display: flex;
      flex-direction: row;
      align-items: center;
      border-radius: 8px;
      border: $border-default;
      padding: $padding08;
      width: 100%;
      @media screen and (min-width: $tab-max-width) {
        display: none;
      }
      &--back {
        i {
          font-size: 24px;
          color: $label-color-primary;
        }
      }
    }
    @media screen and (max-width: 900px) {
      transition: width 0.1s ease-in;
      .car-search-list {
        width: 100%;
        height: calc(100vh - 295px);
        padding: $padding16;
        // overflow-y: scroll;
        display: grid;
        grid-template-columns: repeat(
          auto-fit,
          minmax(330px, 1fr)
        ); /* Adjusts column size based on container width */
        column-gap: $padding16;
        row-gap: $padding16;
        align-items: flex-start;
        justify-content: flex-start;
        &-sort {
          background-color: $background-color-inverse;
          width: fit-content;
          display: flex;
          align-items: center;
          gap: $padding08;
          width: 300px;
          border: $border-default;
          border-radius: $padding08;
          &-header {
            flex-shrink: 0;
            @include title1_medium;
          }
          @media screen and (max-width: $mobile-max-width) {
            width: 130px;
            height: 50px;
          }
        }

        &-header {
          @include body2;
          color: $label-color-secondary;
        }

        &-container-sort {
          display: flex;
          gap: $padding08;
          align-items: stretch;
          width: 100%;
        }

        .car-item-search-container {
          width: 330px;
          margin-left: auto;
          margin-right: auto;
        }
      }
    }
    @media screen and (max-width: 600px) {
      &.collapsed {
        width: 100%;
      }
    }
    @include respond-to("medium") {
      margin-left: auto;
      margin-right: auto;
      width: 90%;

      &.collapsed {
        width: calc(100% - #{$filterWidth});
        margin-left: auto;
        margin-right: unset;

        &.left-align {
          margin-left: unset;
          margin-right: auto;
        }
      }
      transition: width 0.1s ease-in;
      .car-search-list {
        width: 100%;
        // overflow-y: scroll;
        padding-bottom: $padding32;
        &-banner {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 10px;
          align-self: stretch;

          &__subscription {
            height: 90px;
            width: 100%;
            display: flex;
            align-items: center;
            position: relative;
            border-radius: 6px;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
            &--content {
              width: 100%;
              height: 100%;
              padding: $padding12;
              position: absolute;
              top: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }
          }
          &-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            align-self: stretch;
            &-title {
              @include h2;
              line-height: $padding36;
              color: $label-color-success;
              text-align: center;
            }
            &-subtitle {
              @include body2;
              text-align: center;
            }
          }
        }
        &-sort {
          right: 20px;
          z-index: 3;
          width: 300px;
          display: flex;
          align-items: center;
          gap: 10px;
          border: $border-default;
          border-radius: $padding08;
          &-header {
            flex-shrink: 0;
            font-family: "IBM Plex Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
          }
        }
        &-container-sort {
          display: flex;
          justify-content: space-between;
          gap: $padding12;
          width: 100%;
          gap: $padding12;
          width: 100%;
          margin-bottom: $padding08;
        }
        &-sort {
          right: 20px;
          z-index: 3;
          display: flex;
          align-items: center;
          gap: 10px;
          &-header {
            flex-shrink: 0;
            font-family: "IBM Plex Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
          }
        }
        &-header {
          @include body1;
          color: $label-color-secondary;
          &.filterActive {
            padding-top: 10px;
          }
        }
        .car-item-search-container {
          margin-bottom: 0px;
        }
      }
    }
    .car-search-list {
      padding: $padding16;
      &-banner {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        align-self: stretch;

        &-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          align-self: stretch;
          &-title {
            @include h2;
            line-height: $padding36;
            color: $label-color-success;
            text-align: center;
          }
          &-subtitle {
            @include body2;
            text-align: center;
          }
        }
      }
      &-filters {
        &-button {
          @include body1;
          display: flex;
          padding: $padding04 $padding08;
          justify-content: center;
          align-items: center;
          gap: $padding08;
          border-radius: $padding08;
          border: $border-default;
          cursor: pointer;
          white-space: nowrap;
          background-color: $background-color-inverse;

          @media screen and (max-width: $mobile-max-width) {
            @include caption;
            gap: $padding04;
          }
          img {
            height: 16px;
          }
          &-active {
            background-color: $background-color-success;
            border-color: $label-color-success;
            color: $label-color-success;
          }
          &-cross {
            position: relative;
            i {
              color: $label-color-inverse;
              background-color: $background-color-successDark;
              border-radius: 50%;
              padding: $padding02;
            }
          }
        }
      }
      &-filters-container {
        display: flex;
        gap: $padding08;
        width: 100%;
        flex-wrap: wrap;
        @media screen and (max-width: $mobile-max-width) {
          width: calc(100vw - 92px);
          overflow: auto;
          flex-wrap: nowrap;
          padding: $padding08 0;
        }
      }
    }
  }

  .error {
    padding: 32px;
    text-align: center;
    position: relative;
    img,
    svg {
      width: 100%;
      @include respond-to("medium") {
        width: 60%;
      }
    }
    .heading {
      font-size: 20px;
      line-height: 28px;
      font-weight: bold;
      color: $label-color-primary;
    }
    .sub-heading {
      font-size: 14px;
      line-height: 20px;
      color: $label-color-secondary;
    }
    .error-cta {
      margin: 20px auto;
      button {
        border-radius: 4px;
        width: calc(100% - 40px);
        margin: auto;
        background-color: $background-color-successDark;
        color: #fff;
        padding: 15px 32px;
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 0.8px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        outline: none;
      }
    }
  }

  .no-internet {
    background-color: #fdffd4;
    border-bottom: solid 1px #d8d8d8;

    .text {
      padding: 10px 14px;
      font-size: 12px;
      color: #323a44;
    }

    .icon {
      background-color: #f1f3ca;
      padding: 10px 20px;
      font-size: 9px;
      font-weight: bold;
      color: #323a44;
      text-transform: uppercase;
    }
  }
}

.quick-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $padding08;

  &__reset-button {
    white-space: nowrap;
    border-radius: $padding08;
    display: flex;
    padding: $padding12 $padding20;
    gap: $padding04;
    background: $background-color-successDark;
    @include title2-medium;
    color: $label-color-inverse;
    cursor: pointer;
    border: none;
    outline: none;
    width: fit-content;
  }
}
.loading {
  width: 100%;
}

.car-search-list-banner__subscription {
  height: 90px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  &--content {
    width: 100%;
    height: 100%;
    padding: $padding12;
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

@media screen and (max-width: $tab-max-width) {
  .page-search {
    padding: 0;
    padding-top: 60px;
    height: 100%;
    display: block;
    .page-search-right .search-filter-container {
      padding: $padding00 $padding16;
      .search-filter-tabs-container {
        padding: $padding00;
      }
    }
  }
}


