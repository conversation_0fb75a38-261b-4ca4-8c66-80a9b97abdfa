<template>
  <div class="page-search-container">
    <subscription-popup
        :showPopup="showSubscriptionPopup"
        @close="showSubscriptionPopup = false"
        @ctaClick="showSubscriptionPopup = false"
        />
    <SearchTopNav
      v-if="!pageLoading"
      @handleSearchClick="(car) => handleSearchClick(car)"
      @login="
        (callback) => {
          $emit('login', callback);
        }
      "
    />
    <div class="back"></div>
    <!-- <SearchLocationCalendarNew v-if="!pageLoading" :isSearchflow="true" /> -->
    <div class="page-search">
      <search-filters
        @handleFiltersChange="handleFiltersChange"
        @setApiCall="setApiCall"
        ref="searchFilters"
        :filtersNew="filtersNew"
        :isCollapsed="isFilterCollapsed"
        :toggleFilterContainer="toggleFilterContainer"
      />

      <div
        :class="[
          showErrorMsg && !isFilterAcive
            ? 'page-search-error'
            : 'page-search-right',
          { collapsed: true, 'left-align': locale === 'ar' },
        ]"
      >
        <div class="no-internet d-f" v-if="!online">
          <div class="f-1 d-f ai-c jc-c text">
            {{ $t("search_page_no_internet_text") }}
          </div>
          <div class="icon d-f ai-c jc-c fd-c cur-p">
            <i class="zc-ic zc-ic_nointernet"></i>
            <div>{{ $t("waiting") }}</div>
          </div>
        </div>
        <div 
          class="page-search-right-container" 
          :style="{ backgroundColor: isSubscription && isMobile ? '#2A0722' : '' }"
        >
          <div v-if="isTab && !filterLoading" class="car-location">
            <div
              v-if="isTab"
              class="car-location--back"
              @click="handleBackClick"
            >
              <i class="z-arrow_left"></i>
            </div>
            <SearchLocationCalendarNew v-if="isTab" :isSearchflow="true" />
          </div>
          <transition name="fade-banner">
            <img v-if="isSubscription && isMobile"  v-show="!hasScrolled"
                class="page-search-right-container__mweb-subscription-banner" 
                src="/img/subscription/subscription-mweb.svg" 
                alt=""
                @click="showSubscriptionPopup = true"
            />
          </transition>
          <transition name="fade-banner">
          <div v-if="isSubscription && !isMobile" v-show="!hasScrolled" class="car-search-list-banner">
            <div class="car-search-list-banner__subscription" v-if="isSubscription">
              <img src="/img/subscription/subscription-search-bg.png" alt="">
              <div class="car-search-list-banner__subscription--content" @click="showSubscriptionPopup = true">
                <img src="/img/subscription/subscription-search-content.png" alt="">
              </div>
            </div>
            <div class="car-search-list-banner-container" v-else>
              <div class="car-search-list-banner-container-title">
                Daily Drives
              </div>
              <div class="car-search-list-banner-container-subtitle">
                Everyday bookings made quick and easy
              </div>
            </div>
          </div>
          </transition>
          <div v-if="!filterLoading" class="car-search-list-container-sort">
            <SearchCarMweb
              v-if="isMobile"
              @handleSearchClick="handleSearchClick"
            />
            <SearchCar v-else @handleSearchClick="handleSearchClick" />
            <div class="car-search-list-sort" v-if="!showErrorMsg">
              <CustomSelect
                v-if="sortFilters && !isMobile"
                :selectedValue="sortOption"
                :options="sortFilters.options"
                @handleSortSelect="handleSortSelect"
                class="select"
              />
              <SortMweb
                v-if="sortFilters && isMobile"
                :selectedValue="sortOption"
                :sortOptions="sortFilters.options"
                @handleSortSelect="handleSortSelect"
              ></SortMweb>
            </div>
          </div>
          <div class="quick-filters" v-if="!filterLoading">
            <div
              @click="toggleFilterContainer"
              v-if="isMobile"
              class="car-search-list-filters-button"
            >
              <i class="z-filter_funnel"></i>
              <span>Filters</span>
            </div>
            <div class="car-search-list-filters-container">
              <div
                v-for="filter in quickFilters"
                :key="filter.value"
                :class="[
                  'car-search-list-filters-button',
                  {
                    'car-search-list-filters-button-active': filter.selected,
                  },
                ]"
                @click="navFiltersClick(filter)"
              >
                <i v-if="filter.id == 'tagged'" class="z-crown" style="color: #f3b600;"></i>
                <i
                  v-if="!(filter.id == 'tagged')"
                  :class="`z-${filter.icon ? filter.icon.toLowerCase() : ''}`"
                ></i>
                <span>{{ filter.text }}</span>
                <div
                  class="car-search-list-filters-button-cross"
                  v-if="filter.selected"
                  @click.stop="quickFilterCross(filter)"
                >
                  <i class="z-close"></i>
                </div>
              </div>
            </div>
            <div
              class="quick-filters__reset-button"
              v-if="filterCount && !isMobile"
              @click="handleFiltersReset()"
            >
              <span>Reset Filter ({{ filterCount }})</span>
            </div>
          </div>
          <div
            v-if="!filterLoading && !showErrorMsg "
            :class="[
              'car-search-list-header',
              {
                filterActive: isFilterAcive,
              },
            ]"
          >
            {{ responseV8.navbar_title }}
          </div>
        </div>
        <div class="car-search-list-wrapper">
          <div class="loading d-f ai-c jc-c f-1" v-if="filterLoading">
            <div class="loader">
              <div class="loader-background">
                <div class="bg-side"></div>
                <div class="bg-top"></div>
                <div class="bg-middle"></div>
                <div class="bg-bottom"></div>
              </div>
              <div class="loader-background">
                <div class="bg-side"></div>
                <div class="bg-top"></div>
                <div class="bg-middle"></div>
                <div class="bg-bottom"></div>
              </div>
            </div>
          </div>
          <div class="error" v-else-if="showErrorMsg">
            <div
              class="img"
              v-if="responseV8.empty_response && responseV8.empty_response.image"
            >
              <img :src="responseV8.empty_response.image" alt="" />
            </div>

            <div class="heading">
              {{
                responseV8.empty_response
                  ? responseV8.empty_response.header
                  : $t("no_results_found")
              }}
            </div>
            <div class="sub-heading">
              {{
                responseV8.empty_response
                  ? responseV8.empty_response.sub_header
                  : $t("no_results_found")
              }}
            </div>
          </div>
          <div
            v-else-if="cardsV8 && !filterLoading && !pageLoading"
            :class="[
              'car-search-list',
              {
                dual: cardsV8.length === 2,
              },
            ]"
            id="car-search-list"
          >
            <car-item-search
              v-for="(car, index) in cardsV8"
              @handleFavClick="handleFavClick"
              :showFav="true"
              :car="car"
              :key="car.car_data.car_id"
              :itemId="car.car_data.car_id"
              @submit="handleCarSelection"
              @handleSegment="handleSegment"
              :lastCar="lastCar(index)"
              @loadNextCars="loadNextCars"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "./search.scss";

import SearchPage from "./search";
import SortMweb from "../../components/sort-mweb";
export default SearchPage;
</script>