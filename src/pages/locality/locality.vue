<template>
  <main class="locality">
    <!-- Hero Banner Section -->
    <hero-banner-new
      v-if="!isMobile"
      :preTitle="heroBannerPreTitle"
      :title="heroBannerTitle"
      :isLongTitle="true"
    />
    <HomeMwebSearch
      v-else
      @toggleSidebar="$emit('toggleSidebar')"
      @segment="handleSegment"
      :preTitle="heroBannerPreTitle"
      :title="heroBannerTitle"
      :isLongTitle="true"
    />

    <!-- top cars -->
    <home-carousel
      v-if="!filtersLoading"
      :cards="carsData"
      :filters="filters"
      :visibleCards="4"
      :heading="`Top Cars Near You`"
      buttonText="Browse all cars"
      id="car-recommendation"
      @click="browseCars('top cars')"
    >
      <template #filters="{ filter }">
        <div
          class="home-carousel-filter"
          :class="{ 'home-carousel-filter-active': filter.selected }"
          @click="handleFilterClick(filter)"
        >
        <i v-if="filter.id == 'tagged'" class="z-crown" style="color: #f3b600;"></i>
        <i v-if="!(filter.id == 'tagged')" :class="`z-${filter.icon ? filter.icon.toLowerCase() : ''}`"></i>
        <span>{{ filter.text }}</span>
        <div
            class="home-carousel-filter-cross"
            v-if="filter.selected"
            @click.stop="handlefilterCross(filter)"
          >
            <i class="z-close"></i>
          </div>
        </div>
      </template>
      <template
        v-if="carsData && carsData.length >= 1"
        v-slot:default="{ card, index }"
      >
        <car-item-search
          :car="card"
          :key="card.car_data.car_id"
          :itemId="card.car_data.car_id"
          :lastCar="lastCar(index)"
          :showFareSummary="false"
          @submit="handleCarSelection"
          @handleSegment="handleSegment"
          @handleFavClick="handleFavClick"
          :showFav="true"
        />
      </template>
      <template #error>
        <div class="error" v-if="empty_response">
          <div class="heading">
            {{
              empty_response ? empty_response.header : $t("no_results_found")
            }}
          </div>
          <div class="sub-heading">
            {{
              empty_response
                ? empty_response.sub_header
                : $t("no_results_found")
            }}
          </div>
        </div>
      </template>
    </home-carousel>
    <div class="loading d-f ai-c jc-c f-1" v-else>
      <div class="loader">
        <div class="loader-background">
          <div class="bg-side"></div>
          <div class="bg-top"></div>
          <div class="bg-middle"></div>
          <div class="bg-bottom"></div>
        </div>
        <div class="loader-background">
          <div class="bg-side"></div>
          <div class="bg-top"></div>
          <div class="bg-middle"></div>
          <div class="bg-bottom"></div>
        </div>
      </div>
    </div>

    <!-- Locality Information -->
    <section class="locality__info" v-if="localityInfo">
      <h2 class="locality__title">
        {{ `About ${localityDataItem['Locality']}` }}
      </h2>
      <div class="locality__info-content">
        <div class="locality__info-content-text" v-if="localityInfo">{{ localityInfo }}</div>
        <div class="locality__info-content-image">
          <img
            :src="`/img/localities/${localityDataItem['Locality']}.jpeg`"
            :alt="localityDataItem['Locality']"
            loading="lazy"
          />
        </div>
      </div>
    </section>
    <!-- stories -->
    <stories />

    <!-- blogs -->
    <home-carousel
      v-if="blogs.length"
      :cards="blogs"
      :visibleCards="blogs.length"
      :heading="blogHeading"
      id="blogs"
      buttonText="DISCOVER MORE"
      @click="handleBlogCtaClick"
    >
      <template v-slot:default="{ card }">
        <div
          class="locality__blog-card"
          :key="card.id"
          @click="handleBlogCardClick(card)"
        >
          <BlogSvg
            :imageUrl="card.image"
            :id="`${card.id}_image`"
            :key="`${card.id}_image`"
            :isMobile="isMobile"
          />
          <div class="locality__title--blog-card" :key="`${card.id}_title`">
            {{ card.title }}
          </div>
          <div
            class="locality__subtitle--blog-card"
            v-html="card.subTitle"
            :key="`${card.id}_subtitle`"
          ></div>
        </div>
      </template>
    </home-carousel>

    <!-- top cities -->
    <TopCities @segment="handleSegment" />

    <!-- host and app download -->
    <HostAndAppDownload :wrapperStyle="{ gap: isMobile ? '16px' : '60px' }" />

    <!-- footer -->
    <footer-new :hideAboutUs="true" />
  </main>
</template>

<script src="./locality.js"></script>

<style lang="scss" scoped>
@import "./locality.scss";
</style>
