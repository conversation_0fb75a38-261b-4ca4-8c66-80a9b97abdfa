.locality {
  padding-top: 70px;
  .home-carousel-filter {
    @include body1;
    display: flex;
    padding: $padding04 $padding08;
    justify-content: center;
    align-items: center;
    gap: $padding08;
    border-radius: $padding08;
    border: $border-default;
    cursor: pointer;
    white-space: nowrap;
    @media screen and (max-width: $mobile-max-width) {
      @include caption;
      gap: $padding04;
    }
    img {
      height: 16px;
    }
    &-active {
      background-color: $background-color-success;
      border-color: $label-color-success;
      color: $label-color-success;
    }
    &-cross {
      position: relative;
      i {
        color: $label-color-inverse;
        background-color: $background-color-successDark;
        border-radius: 50%;
        padding: $padding02;
      }
    }
  }
  .error {
    text-align: center;
    img {
      width: 100%;
    }
    .heading {
      font-size: 20px;
      line-height: 28px;
      font-weight: bold;
      color: $label-color-primary;
    }
    .sub-heading {
      font-size: 14px;
      line-height: 20px;
      color: $label-color-secondary;
    }
  }
  &__info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: $padding24;
    padding: $padding24;
    margin: $padding64 auto;
    width: 85%;
    background-color: $background-color-primary;
    border-radius: $padding16;
    &-content {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: $padding24;
      flex: row;
      
      @media screen and (max-width: $mobile-max-width) {
        flex-direction: column;
      }
      
      &-text {
        @include subhead;
        font-weight: normal;
        color: $label-color-secondary;
        line-height: 1.6;
        text-align: justify;
        white-space: pre-wrap;

        @media screen and (max-width: $mobile-max-width) {
          @include body1;
          color: $label-color-secondary;
        }
      }
      &-image {
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }

    &-heading {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: flex-start;
      gap: $padding08;

      &--mobile {
        display: none;
      }
    }
  }

  &__title {
    @include h2;
    text-align: center;
    @media screen and (max-width: $mobile-max-width) {
      @include subhead;
    }
  }

  &__blog-card {
    background-color: $background-color-primary;
    cursor: pointer;

    svg:hover {
      transition: all 0.2s ease;

      rect {
        fill: $background-color-black;
      }

      .arrow {
        fill: $label-color-inverse;
      }
    }
  }
  &__title--blog-card {
    @include title1_medium;
    margin-top: $padding24;
  }

  &__subtitle--blog-card {
    @include body2;
    color: $label-color-secondary;
    margin-top: $padding08;
    overflow: hidden;
    max-height: $padding40;
    text-overflow: ellipsis;
    white-space: normal;
  }

  // Attractions Section Styles
  &__attractions {
    width: 85%;
    margin: $padding64 auto;
    padding: $padding24;
    border-radius: $padding16;
    &-heading {
      @include h4;
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: flex-start;
      gap: $padding08;
      margin-bottom: $padding32;
    }

    &-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: $padding24;
      width: 100%;
    }

    &-card {
      background-color: $background-color-inverse;
      border-radius: $padding12;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      cursor: pointer;
      &-text {
        padding: $padding16 $padding20 $padding20;
      }
      &-description {
        @include body2;
        color: $label-color-secondary;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      &-image {
        width: 100%;
        height: 200px;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        &:hover img {
          transform: scale(1.05);
        }
      }
    }
  }
}

@media screen and (max-width: $mobile-max-width) {
  .locality {
    padding-top: 0;

    &__blog-card {
      background-color: $background-color-inverse;
      border: $border-default;
      border-radius: $padding12;
      height: 100%;
      padding: $padding12;
    }

    &__title--blog-card {
      padding: $padding00 $padding16;
    }

    &__subtitle--blog-card {
      padding: $padding00 $padding16;
      margin-bottom: $padding16;
    }
  }
}
