import { format } from "date-fns/esm";
import storage from "~/helpers/storage";

export default function segment() {
  const { Cart, User, Search } = this.$store.state,
    CHECKOUT_ANALYTICS_INFO = {
      Category: "Link_Clicked",
      "New Event Screen": "New Checkout Screen",
      Variant: Cart.experiment_id == 1 ? "one_screen" : "two_screen",
      Screen: "Market Place Checkout",
    };

  const value = new Date().toISOString().split("T");
  const date = value[0];
  const time = value[1].split(".")[0];
  const formatted_value = date + " " + time;
  const referrer = sessionStorage.getItem("referrer") || "";
  const utm_params = JSON.parse(sessionStorage.getItem("utm_params")) || {};

  return {
    get onCheckout() {
      return {
        // 'Event Bucket': 'New Home: Parallel',
        //'Trigger': 'Click on 'Menu' Hamburger',
        Category: "Search_Form",
        "New Event Screen": "Checkout",
        Category_ID: "checkout_form",
        "Start time": format(Cart.data.starts, "YYYY-MM-DD HH:mm:ss"),
        "End time": format(Cart.data.ends, "YYYY-MM-DD HH:mm:ss"),
        Location: Cart.data.location_name,
        context_traits_user_id: User.info.user_id,
        context_traits_phone: User.info.phone,
        context_traits_email: User.info.email,
        City: User.currentCity.link_name,
        // 'Experiment': 'Refer to Experiment Sheet',
        Cargroup: Cart.data.cargroup_id,
        Car: Cart.data.car_id,
        Booking_Type: Cart.data.type,
        // 'Offer': '',
        // 'Offer Rank': '',
        "Location Type": Search.product,
        // 'Service': '-',
        // 'Vehicle Number': '-',
        // 'Booking Status': '-',
        // 'Status': '',
        // 'Rationale': '',
        // 'Comments': ''
        referrer,
      };
    },
    get onPaymentPageRedirect() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        "Event Bucket": "New Checkout	",
        // 'Trigger': 'moves from checkout to payment page',
        "New Event Screen": "New Booking Summary screen ",
        Category_ID: "checkout_to_payment_selection_summary",
        device_id: storage.getItem("device_id"),
        timestamp_data: new Date(),
        timestamp_data_epoch: Date.now(),
        timestamp_formatted_value: formatted_value,
        "Start time": format(Cart.data.starts, "YYYY-MM-DD HH:mm:ss"),
        "End time": format(Cart.data.ends, "YYYY-MM-DD HH:mm:ss"),
        Location: Cart.data.location_name,
        context_traits_user_id: User.info.user_id,
        context_traits_phone: User.info.phone,
        context_traits_email: User.info.email,
        "Booking Id": Cart.data.booking_id,
        "Location Id": Cart.data.location_id,
        City: User.currentCity.link_name,
        "Destination City": null,
        "Flexi Id": Cart.data.flexi_id,
        Promo:
          (Cart.data.offer_params &&
            (Cart.data.offer_params.promo ||
              Cart.data.offer_params.offer_id ||
              Cart.data.offer_params.deal_id)) ||
          Cart.data.promo ||
          Cart.data.offer_id ||
          Cart.data.deal,
        HD: !!Cart.data.hd,
        "Cargroup ID": Cart.data.cargroup_id,
        "Car ID": Cart.data.car_id,
        Booking_Type: Cart.data.type,
        "Location Type": Search.product,
        referrer,
      };
    },
    get onViewOfferClick() {
      return {
        // 'Event Bucket': 'New Home: Parallel',
        //'Trigger': 'Click on 'Menu' Hamburger',
        Category: "Link_Clicked",
        "New Event Screen": "Checkout",
        Category_ID: "auto_apply_checkout_view_offers",
        "Start time": format(Cart.data.starts, "YYYY-MM-DD HH:mm:ss"),
        "End time": format(Cart.data.ends, "YYYY-MM-DD HH:mm:ss"),
        Location: Cart.data.location_name,
        context_traits_user_id: User.info.user_id,
        context_traits_phone: User.info.phone,
        context_traits_email: User.info.email,
        City: User.currentCity.link_name,
        // 'Experiment': 'Refer to Experiment Sheet',
        Cargroup: Cart.data.cargroup_id,
        Car: Cart.data.car_id,
        Booking_Type: Cart.data.type,
        // 'Offer': '',
        // 'Offer Rank': '',
        "Location Type": Search.product,
        // 'Service': '-',
        // 'Vehicle Number': '-',
        // 'Booking Status': '-',
        // 'Status': '',
        // 'Rationale': '',
        // 'Comments': ''
        referrer,
      };
    },
    onLoad(data) {
      const utm_params = JSON.parse(sessionStorage.getItem("utm_params")) || {};
      return {
        // 'Event Bucket': 'New Home: Parallel',
        //'Trigger': 'When checkout page for car booking is loaded',
        Category: "page",
        "New Event Screen": "Checkout",
        Category_ID: "checkout_page",
        device_id: storage.getItem("device_id"),
        timestamp_data: new Date(),
        timestamp_data_epoch: Date.now(),
        timestamp_formatted_value: formatted_value,
        track: "page",
        "Start time": format(Cart.data.starts, "YYYY-MM-DD HH:mm:ss"),
        "End time": format(Cart.data.ends, "YYYY-MM-DD HH:mm:ss"),
        Location: Cart.data.location_name,
        context_traits_user_id: User.info.user_id,
        context_traits_phone: User.info.phone,
        context_traits_email: User.info.email,
        City: User.currentCity.link_name,
        // 'Experiment': 'Refer to Experiment Sheet',
        Car: Cart.data.car_id,
        Cargroup: Cart.data.cargroup_id,
        Booking_Type: Cart.data.type,
        "Location Type": Search.product,
        ...utm_params,
        response: data,
        referrer,
      };
    },
    bookNowClick(data) {
      return {
        Category: "Link_clicked",
        Category_ID: "select_car",
        context_traits_email: User.info.email,
        City: User.currentCity.link_name,
        email_1: User.info.email,
        Event_Screen: "Search",
        Experiment: "1",
        Phone: User.info.phone,
        User_ID: User.info.user_id,
        booking_type: Search.product,
        lat: Search.origin.lat,
        lng: Search.origin.lng,
        Distance: Search.distance,
        d_lat: Search.destination.lat,
        d_lng: Search.destination.lat,
        ...data,
        ...utm_params,
        referrer,
      };
    },
    bookNowClickNonLoggedInUser() {
      return {
        Category: "Link_Clicked",
        Category_ID: "book_now_click_non_logged_in_user",
        device_id: storage.getItem("device_id"),
        timestamp_data: new Date(),
        timestamp_data_epoch: Date.now(),
        timestamp_formatted_value: formatted_value,
        "Event Bucket": "Sanitization Info Page",
        Trigger: "On click of Book Now CTA ``",
        context_traits_user_id: User.info.user_id,
        context_traits_phone: User.info.phone,
        context_traits_email: User.info.email,
        City: User.currentCity.link_name,
        ...utm_params,
        referrer,
      };
    },
    get onCheckoutSummary() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        Category_ID: "checkout_to_booking_summary",
        referrer,
      };
    },
    get checkoutGenericSegmentData() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        referrer,
      };
    },
    get onApplyRemoveCouponClick() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        Category_ID: "checkout_coupon",
        referrer,
      };
    },
    get onCheckoutBackButtonClick() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        Category_ID: "checkout_back_button",
        referrer,
      };
    },
    get onFareBreakupClick() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        Category_ID: "checkout_fare_breakup",
        referrer,
      };
    },
    get onInstaPayClicked() {
      return {
        ...CHECKOUT_ANALYTICS_INFO,
        Category_ID: "checkout_instapay_selected",
        referrer,
      };
    },
    imageSwiped(data) {
      return {
        Category: "Link_Clicked",
        Category_ID: "Image_Swiped",
        Screen: "Market Place Checkout",
        car_id: data.id,
        index: data.index,
        Selected: data.index,
        context_traits_user_id: User.info.user_id,
        device_id: storage.getItem("device_id"),
        cargroup_id: data.cargroup_id,
        referrer,
      };
    },
  };
}
