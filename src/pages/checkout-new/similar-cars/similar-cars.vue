<template>
  <div class="similar-cars-main">
    <div class="upper">
      <div class="title">Similar Listings</div>
    </div>

    <div class="lower">
      <car-item-search
        v-for="(car, index) in formattedCarsData"
        :car="car"
        :key="car.car_data.car_id"
        :itemId="car.car_data.car_id"
        :lastCar="lastCar(index)"
        :showFareSummary="false"
        @submit="handleCarSelection"
        :Carousel="false"
        @handleFavClick="handleFavClick"
        :showFav="true"
      />
    </div>
  </div>
</template>

<script>
import "./similar-cars.scss";
import SimilarCars from "./similar-cars.js";
export default SimilarCars;
</script>
