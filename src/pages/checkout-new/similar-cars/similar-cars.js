import { mapGetters, mapActions } from "vuex";
import cookies from "~/helpers/cookies";
import CarItemSearch from "~/components/car-item-search";
import { microsite } from "~/helpers/microsite/utils";
import { bajajFinservSession } from "~/helpers/microsite/bajajfinserv";
import { airindiaSession } from "~/helpers/microsite/airindia";
import { airindiaexpressSession } from "~/helpers/microsite/airindiaexpress";
import { yonosbiSession } from "~/helpers/microsite/yonosbi";
import {
  AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY,
  BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
  DISPLAY_MICROSITE_APPLY_OFFER_POPUP,
  AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
  YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY,
} from "~/helpers/microsite/constants";
import segment from "../analytics/segment";
export default {
  name: "SimilarCars",
  props: ["sectionData", "newSectionData", "favIds"],
  components: {
    CarItemSearch,
  },
  data() {
    return {
      fav_ids: this.favIds,
      geoAddress: {},
    };
  },
  computed: {
    ...mapGetters({
      user: "User/user",
      isMobile: "App/isMobile",
      authToken: "User/authToken",
      userCityLinkName: "User/userCityLinkName",
      starts: "Search/starts",
      ends: "Search/ends",
      origin: "Search/origin",
      isSubscription: "Search/isSubscription"
    }),
    isLoggedIn() {
      return this.$store.state.User.info.auth_token;
    },
    isMicrosite() {
      return microsite()
    },
    segment() {
      return segment.call(this);
    },
    formattedCarsData() {
      if (!this.newSectionData || !this.newSectionData.length) {
        return [];
      }

      return this.newSectionData.map((car) => ({
        car_data: {
          ...car,
          image_urls: car.images,
          pricing: {
            payable_amount: car.price_line_1?.normal_text,
            fare_header: car.price_line_2?.header,
          },
          location: {
            ...car.location,
            location_id: car.old_checkout_params?.location_id,
            prime_location_id: car.old_checkout_params?.prime_location_id,
            lat: this.origin?.lat,
            lng: this.origin?.lng,
            starts: this.starts,
            ends: this.ends,
            hd_ids: car.location?.hd_ids || [],
            terminal_ids: car.location?.terminal_ids || [],
          },
          add_ons: car.old_checkout_params?.available_add_ons?.map(
            (item) => ({
              label: item,
              id: item,
            })
          ) || [],
        },
      }));
    },
  },
  methods: {
    ...mapActions({
      fetchGeoAddress: "Search/fetchGeoAddress",
      setFavUnfav: "User/setFavUnfav",
    }),
    handleCarSelection(carDetails, car) {
      const { Cart } = this.$store.state;

      this.$emit("onSegment", {
        Category_ID: "SIMILAR_CARS_CLICKED",
        Selected: {
          car_id: carDetails.car_id,
        },
      });

      //first_call is being set as true
      //the idea is that the checkout api is being called for first time when the user checks the checkout summary
      //for the case of returning back to search page from checkout summary page and opening a new car's checkout summary
      //    - it does not trigger a reload, so first_call is false in that case for a new car
      //    - this causes the cta_enabled property as false resulting in checkout button as disabled

      Cart.first_call = true;
      const { city_link } = this.$route.params;
      const pricings = car.pricing || car.pricings;
      const priceIndex = pricings
        ? pricings
            .map((p, i) => ({ ...p, index: i }))
            .filter((p) => p.id == carDetails.price_id)
            .map((p) => p.index)[0]
        : null;

      const addOns = car.car_data.add_ons
        ? car.car_data?.add_ons?.map((item) => item.id)
        : [];

      Cart.data = {
        car: car.car_data.name,
        brand: car.car_data.brand,
        car_id: carDetails.car_id,
        cargroup_id: carDetails.cargroup_id,
        car_price: carDetails.car_price,
        city: city_link,
        starts: carDetails.starts ? carDetails.starts : this.starts,
        ends: carDetails.ends ? carDetails.ends : this.ends,
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        hd: 0,
        offer_id: carDetails.offer_id ? carDetails.offer_id : null,
        radius: carDetails.radius,
        exactLocation: "",
        terminal_id: carDetails.terminal_id,
        hd_location_ids: "",
        user_city: "",
        user_address_id: "",
        last_selected_option: {},
        offer_params: null,
        promo: "",
        section_params: [],
        deal: null,
        seater: carDetails.seater,
        transmission: carDetails.transmission,
        flex_name: carDetails.flex_name,
        hd_unavailable_reason: carDetails.location.hd_unavailable_reason,
        add_ons: addOns,
      };

      if (carDetails.offer_id) {
        Cart.data = {
          offer_params: {
            offer_id: carDetails.offer_id,
          },
        };
      }

      Cart.data = {
        location_id: carDetails.location_id || carDetails.starting_location_id,
        location_name: carDetails.locName,
        type: "round_trip",
        hd_location_ids:
          (carDetails.location.hd_ids.length && carDetails.location.hd_ids) ||
          null,
        hd: (carDetails.location.hd_ids.length && 1) || 0,
        // location_name: encodeURIComponent(this.origin.name),
        lat: this.origin.lat,
        lng: this.origin.lng,
        user_address_id: 0,
        edit: false,

        //reset HD data on new car selection
        address_id: "",
        terminal_id: "",
        section_params: [],
      };

      this.$emit("segment", this.segment.bookNowClick(Cart.data));

      if (microsite()) {
        if (bajajFinservSession()) {
          sessionStorage.setItem(
            BAJAJFINSERV_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaSession()) {
          sessionStorage.setItem(AIRINDIA_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (airindiaexpressSession()) {
          sessionStorage.setItem(
            AIRINDIAEXPRESS_APPLY_REFERRAL_COUPON_CODE_KEY,
            true
          );
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        } else if (yonosbiSession()) {
          sessionStorage.setItem(YONO_SBI_APPLY_REFERRAL_COUPON_CODE_KEY, true);
          sessionStorage.setItem(DISPLAY_MICROSITE_APPLY_OFFER_POPUP, true);
        }
      }

      const params = {
        city_link,
        country_code: this.$store.getters["App/countryCode"],
        car_id: Cart.data.car_id,
        car_name: Cart.routeCarName,
      };

      const query = {
        location_id: carDetails.location.location_id,
        starts: (carDetails.starts
          ? carDetails.starts
          : this.starts
        )?.getTime(),
        ends: (carDetails.ends ? carDetails.ends : this.ends)?.getTime(),
        lat: carDetails.lat || this.origin.lat,
        lng: carDetails.lng || this.origin.lng,
        cargroup_id: carDetails.cargroup_id,
        subscription: this.isSubscription,
      };
      const referrer = sessionStorage.getItem("referrer");
      if (referrer) {
        query.referrer = referrer;
      }

      if (!cookies.read("authToken")) {
        this.$emit("segment", this.segment.bookNowClickNonLoggedInUser());
        Cart.data.address = this.geoAddress.display_name;
        Cart.data.zipcode = this.geoAddress.postcode;
        if (microsite() || this.isMobile) {
          this.$router.push({
            name: "CarDetailPage",
            params,
            query,
          });
        } else {
          const route = this.$router.resolve({
            name: "CarDetailPage",
            params,
            query: {
              ...query,
              available_add_ons: car.car_data?.old_checkout_params?.available_add_ons
            },
          });
          window.open(route.href, "_blank");
        }
      } else {
        Cart.data.selected_terminal = "";
        this.fetchGeoAddress({
          coords: { lat: this.origin.lat, lng: this.origin.lng },
        })
          .then((res) => {
            if (res && res[0]) {
              Cart.data.address = res[0].display_name;
              Cart.data.zipcode = res[0].postcode;
            }
          })
          .catch((err) => {
            return err;
          });
        Cart.data.isTerminal = false;
        if (Cart.data.hd == 0) {
          if (microsite() || this.isMobile) {
            this.$router.push({
              name: "CarDetailPage",
              params,
              query,
            });
          } else {
            const route = this.$router.resolve({
              name: "CarDetailPage",
              params,
              query: {
                ...query,
                available_add_ons: car.car_data?.old_checkout_params?.available_add_ons
              },
            });
            window.open(route.href, "_blank");
          }
        } else {
          this.fetchGeoAddress({
            coords: { lat: this.origin.lat, lng: this.origin.lng },
          })
            .then((res) => {
              if (res && res[0]) {
                Cart.data.address = res[0].display_name;
                Cart.data.locality = res[0].district;
              }
            })
            .catch((err) => {
              return err;
            });
          this.$router.push({
            name: "CarDetailPage",
            params: {
              city_link,
              country_code: this.$store.getters["App/countryCode"],
            },
          });
        }
      }
    },   
    lastCar(index) {
      return index === this.formattedCarsData.length - 1;
    },
    isFav(car) {
      if (!this.isLoggedIn) return false;
      return this.fav_ids.includes(car.car_id);
    },

    handleFavClick(car, is_fav) {
      if (!this.isLoggedIn) {
        this.$emit("handleFavClick");
        return;
      }
      if (is_fav) {
        this.fav_ids = this.fav_ids.filter((id) => id != car.car_id);
      } else {
        this.fav_ids.push(car.car_id);
      }

      // this.isFav = !this.isFav;
      this.setFavUnfav({ id: car.car_id, isFav: !is_fav })
        .then(() => {
          this.$emit("onSegment", {
            Category_ID: "FAV_CAR",
            Selected: {
              car_id: car.car_id,
              isFav: !is_fav,
            },
          });
        })
        .catch((err) => {});
    },
  },

  mounted() {},
};
