<template>

  <div class="loading" v-if="showLoader">
    <div class="loader">
      <div class="loader-background"></div>
      <div class="loader-background"></div>
      <div class="loader-background"></div>
      <div class="loader-background"></div>
    </div>
  </div>
  <div v-else-if="hasError">
    <div :class="['pop-up-container pop-up-small', { 'show-popup': hasError }]">
      <div class="bg-transparent"></div>
      <div class="pop-up-content-container slide-up">
        <div class="close-btn" @click="onModalClose">
          <i class="z-close"></i>
        </div>
        <div class="popup-header">
          <div class="content">
            {{ errorMsg.error_title ? errorMsg.error_title : $t("oops") }}
          </div>
        </div>
        <div class="pop-up-content">
          {{ errorMsg.msg ? errorMsg.msg : $t("something_went_wrong") }}
          <div class="btn-container" @click="onModalClose">
            <div class="try-another-btn">
              {{
                this.errorMsg.error_code == 1018 ? $t("login") : $t("try_again")
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div v-else-if="showOfferSuccessPopup">
    <div :class="['pop-up-container pop-up-small', { 'show-popup': showOfferSuccessPopup }]">
      <div class="bg-transparent"></div>
      <div class="pop-up-content-container slide-up success">
        <div class="close-btn" @click="showOfferSuccessPopup = false">
          <i class="z-close"></i>
        </div>
        <div class="popup-header success">
          <div class="content">
            Success!
          </div>
        </div>
        <div class="pop-up-content success">
          <img src="/img/party-popper.svg" alt="party-popper" />
          <span><b>{{ `${checkoutDatav9?.apply_coupon?.promo}` }}</b> Applied successfully</span>
        </div>
      </div>
    </div>
  </div>
  
  <div v-else class="page-2 page-checkout" :class="$route.name !== 'CarDetailPage' && 'sub-page'">
    <RatingsPopup v-if="showRatingsPopup && checkoutDatav9.sections_data.car_info.reviews"
      :ratingsData="checkoutDatav9.sections_data.car_info.rating" :activeRating="activeRating" :fromRating="fromRating"
      :initialRatings="ratings" @hidePopup="hidePopup" @onSegment="onSegment" />
    <!-- <mweb-popover v-if="showDppPopup && isMobile" @close="showDppPopup = false">
      <div class="dpp__container">
        <div class="dpp__popup-header">
          <div class="content">
            {{ dppPopupData.header.link_action.metadata.title }}
          </div>
        </div>
        <div class="dpp__pop-up-content">
          <div class="dpp__subtitle">
            {{ dppPopupData.header.link_action.metadata.sub_title }}
          </div>
        </div>
        <div class="btn-container" @click="showDppPopup = false">
          <div class="linkdata-btn">
            <div class="button-wrapper">
              <button class="button-primary">
                <span>{{ dppPopupData.header.link_action.metadata.cta }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </mweb-popover> -->
    <subscription-popup
        :showPopup="showSubscriptionPopup"
        @close="showSubscriptionPopup = false"
        @ctaClick="showSubscriptionPopup = false"
        />
    <faq-popup v-if="showFaqPopup" :accordionData="checkoutDatav9.sections_data.faqs.faq_data"
      @handleFaqClick="handleFaqClick" @onSegment="onSegment" />
    <div class="checkout__desktop-popup" v-if="(showRatingInfo || showAssuredPopup) && !isMobile">
      <div class="checkout__desktop-popup--close-btn" @click="
        () => {
          showRatingInfo = false;
          showAssuredPopup = false;
        }
      ">
        <i class="z-close"></i>
      </div>
      <div class="checkout__zc-assured-popup--container" v-if="showAssuredPopup">
        <img class="checkout__zc-assured-popup--img" :src="checkoutDatav9.sections_data.assured.assured_sheet_data.image_url
          " alt="zoomcar assured" />
        <div class="checkout__zc-assured-popup--info">
          <div class="checkout__zc-assured-popup--title">
            {{ checkoutDatav9.sections_data.assured.assured_sheet_data.title }}
          </div>
          <div :key="idx" class="checkout__zc-assured-popup--content" v-for="(benefit, idx) in checkoutDatav9.sections_data.assured
            .assured_sheet_data.items">
            <img :src="benefit.image_url" class="checkout__zc-assured-popup--content-img" />
            <div class="checkout__zc-assured-popup--content-text">
              <div class="checkout__zc-assured-popup--content-title">
                {{ benefit.title }}
              </div>
              <div class="checkout__zc-assured-popup--content-desc">
                {{ benefit.description }}
              </div>
            </div>
          </div>
          <div class="checkout__zc-assured-popup--button" @click="showAssuredPopup = false">
            {{ checkoutDatav9.sections_data.assured.assured_sheet_data.cta }}
          </div>
        </div>
      </div>
      <div class="checkout__ratings-info-popup" v-if="showRatingInfo">
        <div class="checkout__ratings-info-popup--heading">
          {{ ratings.rating.rating_calculation_info.title }}
        </div>
        <img :src="ratings.rating.rating_calculation_info.image_url" alt="" />
        <div class="checkout__ratings-info-popup--info">
          {{ ratings.rating.rating_calculation_info.description }}
        </div>
        <div class="checkout__ratings-info-popup--cta"  @click="showRatingInfo = false" >GOT IT</div>
      </div>
    </div>
    <SlidePopUp v-else-if="(showRatingInfo || showAssuredPopup) && isMobile" :isCustomLayout="true"
      :showPopUp="showAssuredPopup || showRatingInfo" :showTopBorder="false" @modalClosed="
        () => {
          showRatingInfo = false;
          showAssuredPopup = false;
        }
      " class="checkout__popup">
      <div v-if="showAssuredPopup" class="checkout__zc-assured-popup--container">
        <img class="checkout__zc-assured-popup--img" :src="checkoutDatav9.sections_data.assured.assured_sheet_data.image_url
          " alt="zoomcar assured" />
        <div class="checkout__zc-assured-popup--info">
          <div class="checkout__zc-assured-popup--title">
            {{ checkoutDatav9.sections_data.assured.assured_sheet_data.title }}
          </div>
          <div :key="idx" class="checkout__zc-assured-popup--content" v-for="(benefit, idx) in checkoutDatav9.sections_data.assured
            .assured_sheet_data.items">
            <img :src="benefit.image_url" class="checkout__zc-assured-popup--content-img" />
            <div class="checkout__zc-assured-popup--content-text">
              <div class="checkout__zc-assured-popup--content-title">
                {{ benefit.title }}
              </div>
              <div class="checkout__zc-assured-popup--content-desc">
                {{ benefit.description }}
              </div>
            </div>
          </div>
          <div class="checkout__zc-assured-popup--button" @click="showAssuredPopup = false">
            {{ checkoutDatav9.sections_data.assured.assured_sheet_data.cta }}
          </div>
        </div>
      </div>
      <div class="checkout__ratings-info-popup" v-if="showRatingInfo">
        <div class="checkout__ratings-info-popup--heading">
          {{ ratings.rating.rating_calculation_info.title }}
        </div>
        <img :src="ratings.rating.rating_calculation_info.image_url" alt="" />
        <div class="checkout__ratings-info-popup--info">
          {{ ratings.rating.rating_calculation_info.description }}
        </div>
        <div class="checkout__ratings-info-popup--cta" @click="showRatingInfo = false">GOT IT</div>
      </div>
    </SlidePopUp>

    <div class="left" v-if="showSections" ref="left">
      <div class="checkout__top-section">
        <div class="checkout__back" @click="handleBackClick">
          <i class="z-chevron_left"></i> <span>Back</span>
        </div>
        <div class="checkout__fav" @click="handleFavClick(!isFav)">
          <i :class="[
            {
              'z-love': !isFav,
              'z-love_filled': isFav,
              fav: isFav,
            },
          ]"></i>
        </div>
        <div class="checkout__share" @click="shareLink">
          <i class="z-share"></i>
        </div>
      </div>
      <div class="checkout__subscription-mweb" v-if="isMobile && isSubscription">
        <img src="/img/subscription/subscription-text.svg" alt="">
      </div>
      <CarImageNew v-if="checkoutDatav9.sections_data.car_info"
        :carImages="checkoutDatav9.sections_data.car_info.images" @showPopup="HandleShowCarPopup" />
      <ImagePopup :images="checkoutDatav9.sections_data.car_info.images" :index="carImageIndex"
        :showPopup="showCarPopup" @close="showCarPopup = false" />
      <div class="checkout__zc-assured" v-if="'assured' in checkoutDatav9.sections_data">
        <div class="checkout__zc-assured--icon">
          <img :src="checkoutDatav9.sections_data.assured.assured_info.image_url" alt="zoomcar assured" />
        </div>
        <div class="checkout__zc-assured--title">
          {{ checkoutDatav9.sections_data.assured.assured_info.title }}
        </div>
        <div class="checkout__zc-assured--text">
          {{ checkoutDatav9.sections_data.assured.assured_info.description }}.
        </div>
        <div class="checkout__zc-assured--button" @click="showAssuredPopup = true">
          Know More
        </div>
        <div class="checkout__zc-assured--button-chevron" @click="showAssuredPopup = true">
          <i class="z-chevron_right"></i>
        </div>
        
      </div>
      <car-details :newSectionData="checkoutDatav9.sections_data"
        :locationSectionData="checkoutDatav9.offer_request_params" :hostData="hostData" :isMobile="isMobile"
        :activeSection="activeSection" @onSegment="onSegment" @setActiveSection="setActiveSection" />

        <div class="checkout__subscription" v-if="!isMobile && isSubscription">
          <div class="checkout__subscription--title">
            <img src="/img/subscription/subscription-text.svg" alt="">
          </div>
          <div class="checkout__subscription--content">
            <div class="checkout__subscription--value-props">
              <div class="checkout__subscription--value-prop" v-for ="(valueProp, index) in subscriptionValueProps"
                :key="index">
                <div class="checkout__subscription--value-prop-icon">
                  <img :src="valueProp.image_url" alt="">
                </div>
                <div class="checkout__subscription--value-prop-text">
                  {{ valueProp.title }}
                </div>
              </div>
            </div>
            <div class="checkout__subscription--button" @click="subscriptionPopupClick">Know more <i class="z-chevron_right"></i>
            </div>
          </div>

        </div>

        <div class="checkout__subscription" v-if="isMobile && isSubscription">
          <div class="checkout__subscription--title">
            <img src="/img/subscription/subscription-text.svg" alt="">
            <div class="checkout__subscription--button" @click="subscriptionPopupClick">Know more <i class="z-chevron_right"></i>
            </div>
          </div>
          <div class="checkout__subscription--content">
            <div class="checkout__subscription--value-props">
              <div class="checkout__subscription--value-prop" v-for ="(valueProp, index) in subscriptionValueProps"
                :key="index">
                <div class="checkout__subscription--value-prop-icon">
                  <img :src="valueProp.image_url" alt="">
                </div>
                <div class="checkout__subscription--value-prop-text">
                  {{ valueProp.title }}
                </div>
              </div>
            </div>
           
          </div>

        </div>

      <AllCarInfo :sectionData="reorderedSections" :activeSection="activeSection" @onSegment="onSegment"
        @setActiveSection="setActiveSection" />
      <div class="left-sections" id="left-sections">
        <div v-for="sectionData in reorderedSections" :key="sectionData.type">
          <div v-if="sectionData.type === 'OFFERS'" :id="sectionData.type" class="page-checkout-offers-mobile">
            <delivery-product v-if="getDeliveryData" :products="addOns" @click="handleProductClick"
              @address="handleAddressCTAClick" :selectedProduct="selectedDeliveryProduct" v-on:cardUpdated="cardChecked"
              :sectionData="getDeliveryData" />
            <CheckoutOffers :checkoutOffersData="checkoutDatav9.sections_data.wallet_offer_data"
              :bestOffer="bestOffer"
              @removeCoupon="removeCoupon" @onExploreOffers="onExploreOffers" @onApplyBestOffer="onApplyBestOffer"/>
            <zCredits :zCreditsData="checkoutDatav9.sections_data.wallet_offer_data.wallet
              " v-on:cardUpdated="cardChecked" />
            <tppData :dppSectionData="checkoutDatav9.sections_data.benefit_dpp_data"
              @setActiveSection="setActiveSection" />
          </div>

          <Ratings v-if="
            sectionData.type === 'RATING_REVIEW' &&
            checkoutDatav9.sections_data.car_info &&
            checkoutDatav9.sections_data.car_info.rating &&
            checkoutDatav9.sections_data.car_info.reviews
          " :id="sectionData.type" :ratingsData="checkoutDatav9.sections_data.car_info.rating"
            :newSectionData="checkoutDatav9.sections_data.car_info.reviews" @showRatingsPopup="showRatingsPopup = true"
            @setActiveRating="
              (data) => {
                showRatingsPopup = true;
                activeRating = data;
                fromRating = true;
              }
            " @onSegment="onSegment" @ratingInfo="showRatingInfo = true" />

          <CarLocation v-if="sectionData.type === 'LOCATION'" :id="sectionData.type" :sectionData="sectionData"
            :newSectionData="checkoutDatav9.sections_data.car_info.location_data
              " @onSegment="onSegment" />

          <AboutCar v-if="
            sectionData.type === 'FEATURES' &&
            checkoutDatav9.sections_data.car_info.features
          " :id="sectionData.type" :sectionData="sectionData"
            :aboutCarData="checkoutDatav9.sections_data.car_info.features" :isMobile="isMobile"
            @onSegment="onSegment" />
          <dpp v-if="
            sectionData.type === 'BENEFITS' &&
            checkoutDatav9.sections_data.benefit_dpp_data &&
            checkoutDatav9.sections_data.benefit_dpp_data.benefits_data &&
            checkoutDatav9.sections_data.benefit_dpp_data.benefits_data
              .benefit_list
          " :id="sectionData.type" :dppSectionData="checkoutDatav9.sections_data.benefit_dpp_data"
            :sectionData="sectionData" :benefitSectionData="checkoutDatav9.sections_data.benefit_dpp_data &&
              checkoutDatav9.sections_data.benefit_dpp_data.benefits_data &&
              checkoutDatav9.sections_data.benefit_dpp_data.benefits_data
                .benefit_list
              " :benefitData="checkoutDatav9.sections_data.benefit_dpp_data.benefits_data
              " @onSegment="onSegment" @showDppPopup="showDppPopup = true" v-on:cardUpdated="cardChecked"
            @cardUpdated="(data) => $emit('cardUpdated', data)" />

          <Cancellation class="desktop-view" v-if="
            sectionData.type === 'CANCELLATION' &&
            checkoutDatav9.sections_data.cancellation_policy
          " :id="sectionData.type" :sectionData="sectionData" :cancellationSectionData="checkoutDatav9.sections_data.cancellation_policy
              " @onSegment="onSegment" />

          <div class="page-checkout-agreement" v-if="
            sectionData.type === 'AGREEMENT' &&
            checkoutDatav9.sections_data.agreement
          ">
            <single-action class="desktop-view" v-if="
              sectionData.type === 'AGREEMENT' &&
              checkoutDatav9.sections_data.agreement
            " :id="sectionData.type" :agreementData="checkoutDatav9.sections_data.agreement"
              :sectionData="sectionData" v-on:cardUpdated="cardChecked" @onSegment="onSegment" />
          </div>

          <Accordion v-if="sectionData.type === 'FAQ'" :id="sectionData.type"
            :accordionData="checkoutDatav9.sections_data.faqs.faq_data" :faqData="checkoutDatav9.sections_data.faqs"
            @handleFaqClick="handleFaqClick" @onSegment="onSegment" />

          <SimilarCars v-if="sectionData.type === 'SIMILAR_CARS' && showSimilar" :id="sectionData.type"
            :sectionData="sectionData" :newSectionData="similar.cars" :favIds="favIds" @onSegment="onSegment"
            @handleFavClick="handleFavClick" />
        </div>
      </div>
    </div>

    <div class="right" v-if="showSections">
      <div class="right-inner">
        <delivery-product v-if="getDeliveryData" :products="addOns" @click="handleProductClick"
          @address="handleAddressCTAClick" :selectedProduct="selectedDeliveryProduct" v-on:cardUpdated="cardChecked"
          :sectionData="getDeliveryData" />
        <CheckoutOffers :checkoutOffersData="checkoutDatav9.sections_data.wallet_offer_data"
          :bestOffer="bestOffer"
          @removeCoupon="removeCoupon" @onExploreOffers="onExploreOffers" @onApplyBestOffer="onApplyBestOffer"/>
        <zCredits :zCreditsData="checkoutDatav9.sections_data.wallet_offer_data.wallet"
          v-on:cardUpdated="cardChecked" />
        <tppData :dppSectionData="checkoutDatav9.sections_data.benefit_dpp_data" @setActiveSection="setActiveSection" />

        <Summary :summaryData="summaryData" :loadingPayment="loadingPayment"
          :checkCheckoutDisabled="checkCheckoutDisabled" @handlePayment="(data) => handlePayment(data)"
          @showSummary="showSummaryPopup = true" @removeCoupon="removeCoupon" @applyCouponSegment="applyCouponSegment"
          :checkoutData="checkoutDatav9" v-on:cardUpdated="cardChecked" @onSegment="onSegment"
          @update="handleCvIdUpdate"></Summary>
      </div>
    </div>

    <div class="checkout-mobile-footer" :class="{ ar: lang === 'ar' }" v-if="showSections">
      <Summary :summaryData="summaryData" :loadingPayment="loadingPayment"
        :checkCheckoutDisabled="checkCheckoutDisabled" @handlePayment="(data) => handlePayment(data)"
        @showSummary="showSummaryPopup = true" @removeCoupon="removeCoupon" @applyCouponSegment="applyCouponSegment"
        :checkoutData="checkoutDatav9" v-on:cardUpdated="cardChecked" @onSegment="onSegment"
        @update="handleCvIdUpdate" />
    </div>
    <div :class="[
      'pop-up-container fare-summary',
      { 'show-popup': showSummaryPopup },
    ]">
      <div class="bg-transparent"></div>
      <div class="pop-up-content-container slide-up">
        <div class="close-btn" @click="showSummaryPopup = false">
          <i class="z-close"></i>
        </div>
        <div class="popup-header">
          <div class="content" v-if="checkoutDatav9.amount">
            {{ checkoutDatav9.amount.final_amount }}
          </div>
        </div>
        <div class="pop-up-content" v-if="checkoutDatav9.amount && checkoutDatav9.amount.fare_breakup">
          <div v-for="(fare, index) in checkoutDatav9.amount.fare_breakup" :key="index" class="fare">
            <div v-for="(fareItem, index) in fare.fare_item" :key="index">
              <div :class="['fare-item', { highlighted: fareItem.is_highlighted }]" v-if="fareItem.value">
                <div class="header">{{ fareItem.header }}</div>
                <div class="value">{{ fareItem.value }}</div>
              </div>
            </div>
          </div>
          <div class="btn-container" @click="
            handlePayment(
              summaryData.cta ? summaryData.cta.action : undefined
            ),
            (showSummaryPopup = false)
            ">
            <div class="payment-btn">
              <div class="button-wrapper">
                <button class="button-primary" :disabled="loadingPayment || !checkCheckoutDisabled">
                  <span class="loader-ellipsis" v-if="loadingPayment">{{
                    $t("loading")
                    }}</span>
                  <span v-else-if="summaryData">{{
                    summaryData.cta && summaryData.cta.action
                      ? summaryData.cta.action.text
                      : $t("proceed_to_pay")
                  }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div :class="[
      'pop-up-container pop-up-small',
      {
        'show-popup':
          showPopUp &&
          checkoutDatav9.apply_coupon &&
          checkoutDatav9.apply_coupon.popup,
      },
    ]">
      <div class="bg-transparent"></div>
      <div :class="[
        'pop-up-content-container success',
        {
          'slide-up':
            showPopUp &&
            checkoutDatav9.apply_coupon &&
            checkoutDatav9.apply_coupon.popup,
        },
      ]" v-if="checkoutDatav9.apply_coupon && checkoutDatav9.apply_coupon.popup">
        <div class="close-btn" @click="onModalClose">
          <i class="z-close"></i>
        </div>
        <div class="popup-header success">
          <div class="content">
            {{ checkoutDatav9.apply_coupon.popup.header }}
          </div>
        </div>
        <div class="pop-up-content">
          {{ checkoutDatav9.apply_coupon.popup.description }}
        </div>
      </div>
    </div>
    <ExploreOffers
      v-if="showOffers"
      @coupon="handleCoupon"
      @close="handleExploreOffersClose"
      :couponCode="checkoutDatav9.apply_coupon && checkoutDatav9.apply_coupon.promo
        ? checkoutDatav9.apply_coupon.promo : '' "
      :bestOffer="bestOffer"
    />
    <web-popover v-if="showDialog && !isMobile" @close="handleDialogClose">
      <airport-terminal
        :terminals="airport_terminals"
        v-if="selectedDeliveryProduct === 'AIRPORT' || selectedDeliveryProduct === 'PRIME_LOCATION_CD'"
        @click="handleAirportTerminalClick"
        @submit="handleAirportTerminalSelect"
        :terminalId="selectedTerminalId"
      />
      <user-address-list
        v-else-if="(selectedDeliveryProduct === 'HD' || selectedDeliveryProduct === 'CD') && addressScreen === 'list'"
        :list="userAddressList"
        @click="handleAddAddressClick"
        @submit="handleAddressClick"
        @close_city_popup="handleCityPopupClose"
        :differentCityAddressSelected="!sameCityAddressSelected"
      />
      <user-address-details
        v-else-if="(selectedDeliveryProduct === 'HD' || selectedDeliveryProduct === 'CD') && addressScreen === 'add'"
        @save="handleAddressSave"
        :address_details="addressData"
        :differentCityAddressSelected="!sameCityAddressSelected"
        :car_lat="+addressData.lat"
        :car_lng="+addressData.lng"
        @left_click="handleLeftButtonClick"
        @close_city_popup="handleCityPopupClose"
        :isLoggedIn="isLoggedIn"
      />
    </web-popover>
    <mweb-popover v-if="showDialog && isMobile" @close="handleDialogClose">
      <airport-terminal
        :terminals="airport_terminals"
        v-if="selectedDeliveryProduct === 'AIRPORT' || selectedDeliveryProduct === 'PRIME_LOCATION_CD'"
        @click="handleAirportTerminalClick"
        @submit="handleAirportTerminalSelect"
        :terminalId="selectedTerminalId"
      />
      <user-address-list
        v-else-if="(selectedDeliveryProduct === 'HD' || selectedDeliveryProduct === 'CD') && addressScreen === 'list'"
        :list="userAddressList"
        @click="handleAddAddressClick"
        @submit="handleAddressClick"
        @close_city_popup="handleCityPopupClose"
        :differentCityAddressSelected="!sameCityAddressSelected"
      />
      <user-address-details
        v-else-if="(selectedDeliveryProduct === 'HD' || selectedDeliveryProduct === 'CD') && addressScreen === 'add'"
        @save="handleAddressSave"
        :address_details="addressData"
        :differentCityAddressSelected="!sameCityAddressSelected"
        :car_lat="delivery_product_details.car_lat"
        :car_lng="delivery_product_details.car_lng"
        @left_click="handleLeftButtonClick"
        @close_city_popup="handleCityPopupClose"
        :isLoggedIn="isLoggedIn"
      />
    </mweb-popover>

  </div>

</template>


<script>
import "./checkout.scss";

import CheckoutPage from "./checkout";
import ratings from "./ratings/ratings";
export default CheckoutPage;
</script>


<style scoped>
::deep(.v-toast) {
  background-color: black !important;
  color: white !important; /* Optional: Modify the text color */
}

::deep(.v-toast__item) {
  background-color: black !important;
  color: white !important; /* Ensure the text color stays white */
}

::deep(.v-toast__item--success) {
  background-color: black !important;
  color: white !important;
}

::deep(.v-toast__text) {
  color: white !important;
}
</style>

