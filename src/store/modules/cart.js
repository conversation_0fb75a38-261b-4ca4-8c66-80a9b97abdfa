/* @flow */
import { format } from "date-fns/esm";
import cookies from "~/helpers/cookies";
import { defaultDeviceProps, isMobile } from "~/config/defaults";
import { micrositeQueryParam } from "../../helpers/microsite/utils";
import { mod7_airindia } from "../../helpers/math";
import { airindiaSession } from "~/helpers/microsite/airindia";
import { airindiaexpressSession } from "~/helpers/microsite/airindiaexpress";
const qs = require("qs");
const { API_URL } = process.env;
const { API_URL_V4 } = process.env;
const { API_URL_V5 } = process.env;
const { API_URL_V6 } = process.env;
const { API_URL_V9 } = process.env;
const DefaultParams = [...defaultDeviceProps];
const defaultParams = {
  platform: isMobile() ? "mweb" : "web",
  version: 4,
  city: cookies.read("city"),
  device_id: 1000,
  country_code: cookies.read("countryApiCode"),
};

const HEADERS = {
  "Content-Type": "application/json",
};

class CartState {
  constructor() {
    this.name = "Cart";
    this.first_call = true;
    this._data = {
      car: "",
      brand: "",
      car_price: "",
      car_id: null,
      cargroup_id: null,
      location_name: "",
      location_id: null,
      starts: null,
      ends: null,
      city: "",
      search_experiments: null,
      lat: null,
      lng: null,
      hd: null,
      hd_location_ids: null,
      terminal_id: null,
      promo: "",
      booking_id: null,
      offer_id: null,
      deal: null,
      defer_deposit: "",
      user_address_id: "",
      edit: "",
      address: "",
      address_type: "",
      user_city: "",
      locality: "",
      zipcode: "",
      landmark: "",
      type: "",
      exactLocation: "",
      radius: "",
      payableAmount: null,
      last_selected_option: {},
      section_params: null,
      offer_params: null,
      experiment_id: 1,
      flexi_id: "",
      membership_id: null,
      checkout_data: {},
      add_ons: [],
      cv_id: "",
      prime_location_id: null,
    };
  }

  get authToken() {
    return cookies.read("authToken");
  }

  get data() {
    return this._data;
  }

  set data(obj = {}) {
    const objKeys = Object.keys(obj);
    objKeys.forEach((e) => {
      this._data[e] = obj[e];
    });
    const db = window.zcDB;
    db.cart.update(1, this._data);
  }

  get routeCarName() {
    let carName = this._data?.car || this._data?.name || ""
    return carName.toLowerCase().replace(/[\s_,]/g, "-")
  }

  updateTripDates(starts, ends) {
    // Update starts and ends
    this._data.starts = starts;
    this._data.ends = ends;
   
  }

  getV9CheckoutDetails(route , isSubscription=false) {
    let data = {};

    data = this._data;

    cookies.erase("deals");

    let paramsString = window.location.search;
    let searchParams = new URLSearchParams(paramsString);
    let locationId = searchParams.get("location_id") || data.location_id;
    const newParams = DefaultParams.filter((val) => val !== "version=2");
    const city = cookies.read("city") || route.params.city_link
    const starts = data.starts || parseInt(searchParams.get("starts"))
    const ends = data.ends || parseInt(searchParams.get("ends"))

    const params = [
      ...newParams,
      `country_code=${cookies.read("countryApiCode") || "IND"}`,
      `locale=${cookies.read("locale") || "en"}`,
      `city=${city}`,
      // `search_location_id=${locationId}`,
      `search_session_id=${sessionStorage.getItem("search_session_id")}`,
      "version=4",
    ].filter((d) => d);

    //add query param for partner microsite flow
    params.push(micrositeQueryParam());
    let bookingParams = {
      car_id: data.car_id ? data.car_id : null,
      search_location_id: locationId,
      searched_address: data.address,
      searched_location: {
        lat: data.lat ? data.lat : null,
        lng: data.lng ? data.lng : null,
        address: data.address,
        pincode: data.zipcode,
      },
      cargroup_id: data.cargroup_id || searchParams.get("cargroup_id") || null,
      search_experiments: data.search_experiments,
      lat: data.lat ? data.lat : null,
      lng: data.lng ? data.lng : null,
      city:  cookies.read("city")
        ? cookies.read("city")
        : city || null,
      location_id: data.location_id ? data.location_id : null,
      /* Used substring to resolve date conversion issue in firefox */
      starts: starts
        ? typeof starts == "string"
          ? new Date(starts.substring(0, 19)).getTime()
          : typeof starts == "number" ? starts : starts.getTime()
        : null,
      ends: ends
        ? typeof ends == "string"
          ? new Date(ends.substring(0, 19)).getTime()
          : typeof ends == "number" ? ends : ends.getTime()
        : null,
      offer_params: {},
      last_selected_option:
        data && data.last_selected_option ? data.last_selected_option : {},

      addon_params: data.addon_params || {},
      // available_add_ons: data.add_ons,
      search_location_id: locationId,
      prime_location_id: data.prime_location_id || null
    };

    let available_add_ons = data.add_ons
    if(!Array.isArray(data.add_ons)) {
      try {
          let json = JSON.parse(data.add_ons);
          available_add_ons = json
      } catch(e) {
          available_add_ons = [data.add_ons]
      }
    }
    
    bookingParams.available_add_ons = available_add_ons


    if (data.address_id) {
      bookingParams.address_id = data.address_id;
    }

    if (
      data.terminal_id &&
      (typeof data.terminal_id === "number" ||
        typeof data.terminal_id === "string")
    ) {
      bookingParams.terminal_id = data.terminal_id;
    }

    if (data.section_params && data.section_params.length) {
      bookingParams.section_params = data.section_params;
    }

    if (this.first_call) {
      bookingParams = { ...bookingParams, first_call: true };
    }
    if (
      data.deal ||
      (data.offer_params &&
        (data.offer_params.promo ||
          data.offer_params.offer_id ||
          data.offer_params.deal_id))
    ) {
      bookingParams.offer_params = {
        promo:
          data.offer_params && data.offer_params.promo
            ? data.offer_params.promo
            : null,
        offer_id:
          data.offer_params && data.offer_params.offer_id
            ? data.offer_params.offer_id
            : null,
        deal_id:
          data.offer_params && data.offer_params.deal_id
            ? data.offer_params.deal_id
            : data.deal
            ? data.deal
            : null,
      };
    }
    if (isSubscription) {
      if (!bookingParams.available_add_ons.includes("ZOOMCAR_SUBSCRIPTION")) {
        bookingParams.available_add_ons.push("ZOOMCAR_SUBSCRIPTION");
      }
    }
    return fetch(`${API_URL_V9}/bookings/checkout?${params.join("&")}`, {
      method: "POST",
      headers: {
        ...HEADERS,
        "auth-token": this.authToken,
      },
      body: JSON.stringify({
        booking_params: bookingParams,
      }),
    }).then((res) => {
      this.first_call = false;

      const resp = res.json();
      resp.then((res) => {
        this._data.checkout_data = res;
      });

      return resp;
    });
  }

  getPromosNew() {
    const data = this._data;
    const params = [
      ...DefaultParams,
      `city=${cookies.read("city")}`,
      `locale=${cookies.read("locale")}`,
      `country_code=${cookies.read("countryApiCode")}`,
      `segment=all`,
      `booking_starts=${
        data.starts
          ? typeof data.starts == "string"
            ? new Date(data.starts.substring(0, 19)).getTime()
            : data.starts.getTime()
          : null
      }`,
      `booking_ends=${
        data.ends
          ? typeof data.ends == "string"
            ? new Date(data.ends.substring(0, 19)).getTime()
            : data.ends.getTime()
          : null
      }`,
      `car_id=${data.car_id}`,
      `type=${data.type ?? "normal"}`,
      `cargroup_id=${data.cargroup_id}`,
      `user_lat=${data.lat}`,
      `user_lng=${data.lng}`,
      `trip_fare=${data.checkout_data?.amount?.trip_fare_after_discounts}`,
    ];
    if (Array.isArray(data.add_ons) && data.add_ons.length > 0) {
      let available_add_ons = "["
      data.add_ons.forEach((addOn, i) => {
        if(i == 0) {
          available_add_ons += `"${addOn}"`
        } else {
          available_add_ons += `, "${addOn}"`
        }
      });
      available_add_ons += "]"

      params.push(`available_add_ons=${available_add_ons}`)
    } else if (typeof data.add_ons == "string"){
      params.push(`available_add_ons=["${data.add_ons}"]`)
    }

    let bookingCreatedAt = data.checkout_data?.offer_request_params?.booking_created_at
    if (bookingCreatedAt) {
      params.push(`booking_created_at=${bookingCreatedAt}`)
    }

    return fetch(`${API_URL_V6}/offers/checkout_list?${params.join("&")}`, {
      headers: {
        "auth-token": this.authToken,
      },
    })
      .then((res) => res.json())
      .catch((err) => {
        throw new Error(err);
      });
  }

  getBestOffer(res) {
    const allOffers = res?.sections?.reduce((acc, {snippet_data=[]}) => {
      return [...acc, ...snippet_data]
    }, [])
    
    const bestOffer = allOffers.sort((a, b) => b.savings.amount - a.savings.amount)
    return bestOffer?.[0].state != "DISABLED" ? bestOffer[0] : null
  }

  toTimestamp = (strDate) => {
    return Date.parse(strDate);
  };

  doCreate() {
    const data = this._data;

    if (!data.lat || !data.lng || !data.address) {
      window.Bugsnag && window.Bugsnag.notify(data);
    }
    let paramsString = window.location.search;
    let searchParams = new URLSearchParams(paramsString);
    let locationId = searchParams.get("location_id");

    const queryObject = {
      ...defaultParams,
      auth_token: this.authToken,
      booking_params: {
        searched_location: {
          lat: data.lat,
          lng: data.lng,
          address: data.address,
          pincode: data.zipcode,
        },
        car_id: data.car_id ? data.car_id : null,
        cargroup_id: data.cargroup_id ? data.cargroup_id : null,
        lat: data.lat ? data.lat : null,
        lng: data.lng ? data.lng : null,
        city: data.city ? data.city : null,
        location_id: data.location_id ? data.location_id : null,
        starts: data.starts ? +this.toTimestamp(data.starts) : null,
        ends: data.ends ? this.toTimestamp(data.ends) : null,
        type: data.type ? data.type : null,
        // available_add_ons: data.add_ons,
        offer_params: {},
        addon_params: data.addon_params || {},
        search_location_id: locationId,
        prime_location_id: data.prime_location_id || null,
      },
    };

    let available_add_ons = data.add_ons
    if(!Array.isArray(data.add_ons)) {
      try {
          let json = JSON.parse(data.add_ons);
          available_add_ons = json
      } catch(e) {
          available_add_ons = [data.add_ons]
      }
    }

    queryObject.booking_params.available_add_ons = available_add_ons

    if (data.address_id) {
      queryObject.booking_params.address_id = data.address_id;
    }

    if (
      data.terminal_id &&
      (typeof data.terminal_id === "number" ||
        typeof data.terminal_id === "string")
    ) {
      queryObject.booking_params.terminal_id = data.terminal_id;
    }

    if (data.section_params && data.section_params.length) {
      queryObject.booking_params.section_params = data.section_params;
    }
    if (airindiaSession() && mod7_airindia(data.cv_id)) {
      queryObject.booking_params.cv_id = Number(data.cv_id);
    }
    if (airindiaexpressSession() && mod7_airindia(data.cv_id)) {
      queryObject.booking_params.cv_id = Number(data.cv_id);
    }
    if (
      data.deal ||
      (data.offer_params &&
        (data.offer_params.promo ||
          data.offer_params.offer_id ||
          data.offer_params.deal_id))
    ) {
      queryObject.booking_params.offer_params = {
        promo:
          data.offer_params && data.offer_params.promo
            ? data.offer_params.promo
            : null,
        offer_id:
          data.offer_params && data.offer_params.offer_id
            ? data.offer_params.offer_id
            : null,
        deal_id:
          data.offer_params && data.offer_params.deal_id
            ? data.offer_params.deal_id
            : data.deal
            ? data.deal
            : null,
      };
    }

    if (queryObject.booking_params.city == null && cookies.read("city")) {
      queryObject.city = cookies.read("city");
    }

    const params = [
      ...DefaultParams,
      `city=${cookies.read("city")}`,
      `country_code=${cookies.read("countryApiCode")}`,
      `locale=${cookies.read("locale")}`,
      `auth_token=${this.authToken}`,
      `search_session_id=${sessionStorage.getItem("search_session_id")}`,
      `version=4`
    ];

    //add query param for partner microsite flow
    params.push(micrositeQueryParam());

    return fetch(`${API_URL_V5}/bookings?${params.join("&")}`, {
      method: "POST",
      headers: {
        ...HEADERS,
        "auth-token": this.authToken,
      },
      body: JSON.stringify(queryObject),
    }).then((res) => res.json());
  }

  addressList(niSelfServeId = undefined) {
    const data = this._data;
    const params = [
      ...DefaultParams,
      `country_code=${cookies.read("countryApiCode")}`,
      `locale=${cookies.read("locale")}`,
      `defer_deposit=${data.defer_deposit}`,
      `auth_token=${this.authToken}`,
      `page_name=${niSelfServeId ? "ni_self_serve" : null}`,
      data.lat ? `lat=${data.lat}` : null,
      data.lng ? `lng=${data.lng}` : null,
      data.car_id ? `car_id=${data.car_id}` : null,
      data.cargroup_id ? `cargroup_id=${data.cargroup_id}` : null,
      data.promo ? `promo=${data.promo}` : null,
      data.location_id ? `location_id=${data.location_id}` : null,
      data.starts
        ? `starts=${encodeURIComponent(
            format(data.starts, "YYYY-MM-DD HH:mm")
          )}`
        : null,
      data.ends
        ? `ends=${encodeURIComponent(format(data.ends, "YYYY-MM-DD HH:mm"))}`
        : null,
      data.city ? `city=${data.city}` : null,
      data.lat ? `d_lat=${data.lat}` : null,
      data.lng ? `d_lng=${data.lng}` : null,
      data.hd ? `hd=${data.hd}` : null,
      data.offer_id ? `offer_id=${data.offer_id}` : null,
      data.hd_location_ids ? `hd_location_ids=[${data.hd_location_ids}]` : null,
      data.terminal_id ? `terminal_id=${data.terminal_id}` : null,
      data.user_address_id ? `user_address_id=${data.user_address_id}` : null,
      data.edit ? `edit=${data.edit}` : null,
      data.address ? `address=${data.address}` : null,
      data.address_type ? `address_type=${data.address_type}` : null,
      data.user_city ? `user_city=${data.user_city}` : null,
      data.locality ? `locality=${data.locality}` : null,
      data.zipcode ? `zipcode=${data.zipcode}` : null,
      data.landmark ? `landmark=${data.landmark}` : null,
    ].filter((d) => d);

    //add query param for partner microsite flow
    params.push(micrositeQueryParam());

    return fetch(`${API_URL_V4}/user_addresses/list?${params.join("&")}`, {
      method: "POST",
    }).then((res) => res.json());
  }

  addressCreation() {
    const data = this._data;
    const queryObject = {
      ...defaultParams,
      auth_token: this.authToken,
      address: {
        type: data.address_type ? data.address_type : null,
        description: data.description ? data.description : null,
        address_lines: data.address ? data.address : null,
        locality: data.locality ? data.locality : null,
        zipcode: data.zipcode ? data.zipcode : null,
        landmark: data.landmark ? data.landmark : null,
        city: data.user_city ? data.user_city : null,
        lat: data.lat ? data.lat : null,
        lng: data.lng ? data.lng : null,
      },
    };

    const query = qs.stringify(queryObject);

    return fetch(`${API_URL_V4}/users/address?${query}`, {
      method: "POST",
    }).then((res) => res.json());
  }

  addressUpdate(address_id) {
    const data = this._data;
    const queryObject = {
      ...defaultParams,
      auth_token: this.authToken,
      address: {
        type: data.address_type ? data.address_type : null,
        description: data.description ? data.description : null,
        address_lines: data.address ? data.address : null,
        locality: data.locality ? data.locality : null,
        zipcode: data.zipcode ? data.zipcode : null,
        landmark: data.landmark ? data.landmark : null,
        city: cookies.read("city"),
        lat: data.lat ? data.lat : null,
        lng: data.lng ? data.lng : null,
      },
    };

    const query = qs.stringify(queryObject);

    return fetch(`${API_URL_V4}/users/address/${address_id}?${query}`, {
      method: "PUT",
    }).then((res) => res.json());
  }

  addressDelete(address_id) {
    const params = [
      ...DefaultParams,
      `country_code=${cookies.read("countryApiCode")}`,
      `locale=${cookies.read("locale")}`,
      `auth_token=${this.authToken}`,
      `address_id=${address_id}`,
    ];
    //add query param for partner microsite flow
    params.push(micrositeQueryParam());

    return fetch(`${API_URL_V4}/user_addresses/delete?${params.join("&")}`, {
      method: "POST",
    }).then((res) => res.json());
  }

  getBookingConfirmationDetails(booking_id) {
    let bookingId = booking_id
      ? booking_id
      : this._data.booking_id
      ? this._data.booking_id
      : null;
    if (bookingId) {
      const params = [
        ...DefaultParams,
        `locale=${cookies.read("locale")}`,
        `country_code=${cookies.read("countryApiCode")}`,
      ];

      params.push(micrositeQueryParam());

      return fetch(
        `${API_URL_V5}/bookings/${booking_id}/confirmation_details?${params.join(
          "&"
        )}`,
        {
          method: "GET",
          headers: {
            "auth-token": this.authToken,
          },
        }
      ).then((res) => res.json());
    }
  }

  fetchReviews(car_id,sort_by='') {
    this.loading = true;
    this.error = null;

 

    const params = [
     ...DefaultParams,
     `sort_by=${sort_by}`,
     `locale=${cookies.read("locale")}`,
     `country_code=${cookies.read("countryApiCode")}`,
     `city=${cookies.read("city")}`
    ];

    const url = `${API_URL_V5}/cars/${car_id}/reviews?${[...params].join('&')}`;

    return fetch(url)
      .then((res) => {
        if (!res.ok) {
          throw new Error('Failed to fetch reviews');
        }
        return res.json();
      })
  }

  fetchAirportTerminals() {
    const params = [
      ...DefaultParams,
      `locale=${cookies.read("locale")}`,
      `country_code=${cookies.read("countryApiCode")}`,
    ];
    return fetch(
      `${API_URL_V5}/cities/${cookies.read(
        "city"
      )}/airport_terminals?${params.join("&")}`
    ).then((res) => res.json());
  }

  fetchUserAddresses() {
    const params = [
      ...DefaultParams,
      `locale=${cookies.read("locale")}`,
      `country_code=${cookies.read("countryApiCode")}`,
    ];
    const headers = {};
    if (localStorage.getItem("access_token")) {
      headers["user-access-token"] = localStorage.getItem("access_token");
    }
    return fetch(`${API_URL}/auth/users/addresses?${params.join("&")}`, {
      method: "GET",
      headers,
    }).then((res) => {
      if (!res.ok) {
        return res.text().then(errorText => {
          throw errorText;
        });
      }
      return res.json()});
  }
  createAddress(data) {
    return fetch(`${API_URL}/auth/users/addresses`, {
      method: "POST",
      headers: {
        ...HEADERS,
        "user-access-token": localStorage.getItem("access_token"),
      },
      body: JSON.stringify({
        address: data,
      }),
    }).then((res) => res.json());
  }
}

export default {
  state: new CartState(),
};