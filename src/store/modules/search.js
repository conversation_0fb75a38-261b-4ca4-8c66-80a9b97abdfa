/* @flow */
import axios from "axios";
// import { format, addMinutes } from "date-fns/esm";
import randomstring from "randomstring";
import storage from "~/helpers/storage";
import { roundUpTime } from "~/helpers/time";
import { defaultDeviceProps, isMobile } from "~/config/defaults";
import cookies from "~/helpers/cookies";
import routes from "../../routes/index";
import { micrositeQueryParam } from "../../helpers/microsite/utils";

const { API_URL_V4 } = process.env;
const { API_URL_V5 } = process.env;
const { API_URL_V6 } = process.env;
const { API_URL } = process.env;
const { API_URL_STAGING } = process.env;
const { GEOSERVICE_TOKEN } = process.env;
const API_URL_V8 = API_URL + "/v8";
const DefaultParams = defaultDeviceProps;
let cancelToken;
const router = routes();

// const platform = isMobile() ? "mweb" : "web";

const state = {
  name: "Search",
  products: undefined,
  product: storage.getItem("product") || "round_trip",
  origin: {
    city: undefined,
    name: undefined,
    lat: undefined,
    lng: undefined,
    setByLocality: false, // Flag to track if origin was set by locality coordinates
    lastSetTimestamp: null, // Timestamp when origin was last set
  },
  destination: {
    city: undefined,
    name: undefined,
    lat: undefined,
    lng: undefined,
  },
  products_lead_time: undefined,
  minStarts: undefined,
  starts: undefined,
  ends: undefined,
  distance: 0,
  duration: 0,
  min_booking_duration: undefined,
  normal_min_booking_duration: undefined,
  subscription_min_booking_duration: undefined,
  long_weekend_data: null,
  suggested: {
    suggested_pickup_locations: [],
  },
  geo_session_token: null,

  pagination: {
    has_more: false,
    index: null,
    cache_key: null,
  },
  city_id: 1,

  fts: {
    name: null,
    value: null,
    trending: true,
  },
  home_products: [],
  isSubscription: false,
  selectedProduct: "NORMAL",
};

const getters = {
  productTitle: (state) => {
    const title =
      state.products &&
      (state.products.find((product) => product.name === state.product) || {})
        .redirect_page;
    return title && title.replace("_", " ").toLowerCase();
  },

  products: (state) => {
    if (!state.products) return;
    const availableProducts = ["round_trip", "airport"];
    return state.products.filter((product) =>
      availableProducts.includes(product.name)
    );
  },

  product: (state) => state.product,

  minStarts: (state) => state.minStarts,

  starts: (state) => state.starts,

  ends: (state) => state.ends,

  origin: (state) => state.origin,

  isSubscription: (state) => state.isSubscription,

  homeProductsTabs: (state) => state.home_products,

  destination: (state) => state.destination,

  originCity: (state) => state.origin.city,

  destinationCity: (state) => state.destination.city,

  // dropOffCities: state => state.destination.suggested.drop_off_cities,

  suggested: (state) => state.suggested,

  distance: (state) => state.distance,

  duration: (state) => state.duration,

  minDuration:
    (state) =>
    (startDate = state.starts) => {
      const { min_booking_duration, long_weekend_data, starts, isSubscription ,subscription_min_booking_duration } = state;
      let minDuration = isSubscription ? subscription_min_booking_duration :( min_booking_duration || 240);
      let updatedStarts = startDate
      if(updatedStarts && typeof updatedStarts == "string") {
        updatedStarts = new Date(updatedStarts)
      }
      if (long_weekend_data && updatedStarts && !isSubscription) {
        long_weekend_data.map((data) => {
          if (
            updatedStarts.getTime() >= data.starts &&
            updatedStarts.getTime() <= data.ends
          ) {
            minDuration = data.min_booking_duration || minDuration;
          }
        });
      }
      return minDuration;
    },

  apiParams:
    (state, getters, rootState) =>
    (opts = {}) => {
      let params = [
        ...DefaultParams,
        `starts_epoch=${state.starts.getTime()}`,
        `ends_epoch=${state.ends.getTime()}`,
        `city=${opts.city || state.origin.city}`,
        `bracket=${opts.bracket || "no_fuel"}`,
        "zap=true",
        `type=${state.product} || 'round_trip'`,
        `country_code=IND`,
        `locale=${cookies.read("locale") || "en"}`,
        `search_session_id=${sessionStorage.getItem("search_session_id")}`,
      ];

      //add query param for partner microsite flow
      params.push(micrositeQueryParam());

      if (opts.flex_name) {
        params = [...params, `flex_name=${opts.flex_name}`];
      }
      if (Object.keys(opts.selectedFilters).length) {
        let filterParam = [];
        for (const [key, value] of Object.entries(opts.selectedFilters)) {
          let x = `${key + "=" + value}`;
          filterParam.push(x);
        }
        params = [...params, ...filterParam];
      }
      if (opts.selectedCategories.length) {
        params = [...params, opts.selectedCategories[0]];
      }

      params = [
        ...params,
        `lat=${state.origin.lat}`,
        `lng=${state.origin.lng}`,
      ];

      const { car_id, city_id, lat_e5, lng_e5, location_id, cargroup_id } =
        router.currentRoute.query;

      if (car_id) {
        params.push(`seek.car_id=${car_id}`);
        params.push(`select.cars=${car_id}`);
      }
      if (city_id) params.push(`seek.city_id=${city_id}`);
      if (lat_e5) params.push(`seek.lat_e5=${lat_e5}`);
      if (lng_e5) params.push(`seek.lng_e5=${lng_e5}`);
      if (location_id) params.push(`seek.location_id=${location_id}`);
      if (cargroup_id) params.push(`select.cargroups=${cargroup_id}`);

      return params.join("&");
    },
  apiParamsV8:
    (state, getters, rootState) =>
    (opts = {}) => {
      let params = [
        // ...DefaultParams,
        `platform=ios`,
        `version=17.1.0`,
        `device_id=${storage.getItem("device_id") || uuidv4()}`,
        "device_name=000",
        `start_epoch_millis=${state.starts.getTime()}`,
        `end_epoch_millis=${state.ends.getTime()}`,
        `city=${opts.city || state.origin.city}`,
        `type=round_trip`,
        `country_code=IND`,
        `locale=${cookies.read("locale") || "en"}`,
        `search_session_id=${sessionStorage.getItem("search_session_id")}`,
      ];
      const isTarget = opts.selectedFilters["target.car_id"] ? true : false;

      if (state.pagination.index && !isTarget) {
        params.push(`index=${state.pagination.index}`);
      }

      if (state.pagination.cache_key && !isTarget) {
        params.push(`cache_key=${state.pagination.cache_key}`);
      }

      if (Object.keys(opts.selectedFilters).length) {
        let filterParam = [];
        for (const [key, value] of Object.entries(opts.selectedFilters)) {
          let x = `${key + "=" + value}`;
          filterParam.push(x);
        }
        params = [...params, ...filterParam];
      }

      if (state.fts?.value) {
        params.push(`fts=${state.fts.value}`);
      }

      params = [
        ...params,
        `lat=${state.origin.lat}`,
        `lng=${state.origin.lng}`,
      ];

      return params.join("&");
    },

  pagination: (state, getters, rootState) => state.pagination,

  city_id: (state) => state.city_id,
  fts: (state) => state.fts,
};

const actions = {
  fetchSuggested({ commit, state }, source) {
    const city = state.origin.city || cookies.read("city");
    if (city) {
      commit("setsuggested", {
        suggested_pickup_locations: [],
      });
      return axios({
        method: "GET",
        url: `${API_URL}/geoservice/v1/places/suggestions`,
        headers: {
          "auth-token": `Bearer ${GEOSERVICE_TOKEN}`,
        },
        params: {
          city,
        },
      })
        .then((res) => res.data)
        .then((res) => {
          if (res.message === "SUCCESS") {
            if (state.product === "round_trip" || state.product === "airport") {
              commit("setsuggested", {
                suggested_pickup_locations: res.data,
              });
            }
          }
        })
        .catch(() => {});
    }
  },

  fetchDistanceMatrix: ({ commit, state, rootState }) => {
    const params = [
      ...DefaultParams,
      `lat=${state.origin.lat}`,
      `lng=${state.origin.lng}`,
      `d_lat=${state.destination.lat}`,
      `d_lng=${state.destination.lng}`,
      `country_code=IND`,
      `locale=${cookies.read("locale")}`,
      `starts=${
        typeof state.starts === "string"
          ? new Date(state.starts).getTime()
          : state.starts.getTime()
      }`,
    ];

    return axios
      .get(`${API_URL_V5}/cities/distancematrix?${params.join("&")}`)
      .then((res) => res.data)
      .then((res) => {
        if (res.status) {
          commit("setDistance", res.distance);
          commit("setDuration", res.duration);
          return res;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  },

  fetchCars: (
    { getters },
    {
      city = "",
      bracket,
      flex_name = "",
      selectedFilters = {},
      selectedCategories = [],
    }
  ) => {
    //Check if there are any previous pending requests
    if (typeof cancelToken != typeof undefined) {
      cancelToken.cancel("Operation canceled due to new request.");
    }

    //Save the cancel token for the current request
    cancelToken = axios.CancelToken.source();

    const headers = {};
    if (localStorage.getItem("access_token"))
      headers["user-access-token"] = localStorage.getItem("access_token");

    return axios
      .get(
        `${API_URL_V6}/search?${getters["apiParams"]({
          city,
          bracket,
          flex_name,
          selectedFilters,
          selectedCategories,
        })}`,
        { headers },
        { cancelToken: cancelToken.token }
      )
      .then((res) => res.data)
      .catch((res) => res.response.data);
  },

  fetchV8Cars: (
    { getters },
    { city = "", selectedFilters = {}, selectedCategories = [] }
  ) => {
    const endpoint = state.isSubscription ? "/search/zoomplus" : "/search";
    return axios.get(
      `${API_URL_V8}${endpoint}?${getters["apiParamsV8"]({
        city,
        selectedFilters,
        // selectedCategories,
      })}`,
      {
        headers: {
          ["user-access-token"]: localStorage.getItem("access_token"),
        },
      }
    );
  },

  fetchTrendingSearch: (
    { commit, state, rootState },
    { type = null, text = "", cache_key = state.pagination.cache_key }
  ) => {
    const params = [
      ...DefaultParams,
      `lat=${state.origin.lat}`,
      `lng=${state.origin.lng}`,
      `city=${cookies.read("city")}`,
      `city_id=${state.city_id}`,
      `country_code=IND`,
      `locale=${cookies.read("locale") || "en"}`,
      `search_session_id=${sessionStorage.getItem("search_session_id")}`,
      `cache_key=${cache_key}`,
    ];

    if (type === "search") {
      params.push(`param=${text}`);
      return axios
        .get(`${API_URL_V8}/search/fts/search?${params.join("&")}`)
        .then((res) => res.data)
        .then((res) => {
          return res;
        });
    }

    return axios
      .get(`${API_URL_V8}/search/fts/base?${params.join("&")}`)
      .then((res) => res.data)
      .then((res) => {
        return res;
      });
  },
  fetchZoomcarSubscription: ({ commit, state, rootState }) => {
    const params = [
      ...DefaultParams,
    ];
    return axios
      .get(`${API_URL_V5}/zoomcar_subscription/list?${params.join("&")}`).
      then(res => res.data)
      .catch(err => {
        console.error("Error fetching Zoomcar Subscription:", err);
      });
  },
  fetchSearched: ({ commit, rootState }, { text }) => {
    const city = state.origin.city || cookies.read("city");
    const countryApiCode =
      rootState.App.countryApiCode || cookies.read("countryApiCode");
    if (city && countryApiCode) {
      const headers = {
        "Geo-Provider": "here",
        "auth-token": `Bearer ${GEOSERVICE_TOKEN}`,
      };
      return axios
        .get(
          `${API_URL}/geoservice/v1/places/autocomplete?query=${text}&country_code=${countryApiCode}&city=${city}&types=establishment&locale=${cookies.read(
            "locale"
          )}&geo_session_token=${state.geo_session_token}`,
          { headers }
        )
        .then((res) => {
          if (res.status === 200) return res?.data?.data?.places;
        });
    }
  },
  fetchPlaceDetails: ({ rootState }, { place_id }) => {
    const headers = {
      "Geo-Provider": "here",
      "auth-token": `Bearer ${GEOSERVICE_TOKEN}`,
    };
    return axios
      .get(
        `${API_URL}/geoservice/v1/places/details?place_id=${place_id}&locale=${cookies.read(
          "locale"
        )}&geo_session_token=${state.geo_session_token}`,
        { headers }
      )
      .then((res) => {
        if (res.status === 200) return res?.data?.data;
      });
  },
  fetchGeoAddress: ({}, { coords }) => {
    const headers = {
      "Geo-Provider": "here",
      "auth-token": `Bearer ${GEOSERVICE_TOKEN}`,
    };
    if (coords.lat && coords.lng) {
      return axios
        .get(
          `${API_URL}/geoservice/v2/address?lat=${coords.lat}&lon=${
            coords.lng
          }&language=${cookies.read("locale") || "en"}${
            sessionStorage.getItem("referrer") === "smartbuy"
              ? "&affiliate=smartbuy"
              : ""
          }`,
          { headers }
        )
        .then((res) => {
          if (res.status === 200) return res?.data?.data;
        });
    }
  },
  setProductType({ commit }, product, changeDate = true) {
    const isSubscription = product.type === "ZOOMCAR_SUBSCRIPTION";
    state.isSubscription = isSubscription;
    state.selectedProduct = product.type;
    sessionStorage.setItem("product_type", product.type);

    // Calculate minimum duration based on product type
    const minDuration = isSubscription
      ? product.min_duration || state.subscription_min_booking_duration
      : state.normal_min_booking_duration || 240;

    state.min_booking_duration = minDuration;

    // Calculate end date
    let endDate = new Date(state.starts.getTime());
    const proposedEndDate = new Date(
      state.starts.getTime() + minDuration * 60 * 1000
    );

    if (isSubscription) {
      endDate = state.ends < proposedEndDate ? proposedEndDate : state.ends;
    } else {
      endDate = proposedEndDate;
    }

    if (changeDate) {
      state.ends = endDate;
    }
  },
  setSubscription({ commit, dispatch }, isSubscription) {
    const product = state.home_products.find(
      (product) =>
        product.type === (isSubscription ? "ZOOMCAR_SUBSCRIPTION" : "NORMAL")
    );
    if (product) {
      const isSubscription = product.type === "ZOOMCAR_SUBSCRIPTION";
    state.isSubscription = isSubscription;
    state.selectedProduct = product.type;
    sessionStorage.setItem("product_type", product.type);
  
    const minDuration = isSubscription 
      ? product.min_duration || state.subscription_min_booking_duration || 10080
      : state.normal_min_booking_duration || 240;
    
    state.min_booking_duration = minDuration;
  
  
    }
  },
};

const mutations = {
  updateOrigin: (state, payload) => {
    if (!payload) return;
    const {
      products,
      link_name,
      products_lead_time,
      min_booking_duration,
      long_weekend_data,
      home_product_tabs,
    } = payload;
    state.products = products;
    state.home_products = home_product_tabs;
    const currentProduct = state.product;
    const isCurrentProductAvailable = products?.find(
      (product) => product.name === currentProduct
    );
    const defaultProduct = (products?.find((product) => product.default) || {})
      .name;
    state.product = isCurrentProductAvailable ? currentProduct : defaultProduct;

    state.origin.city = link_name;
    state.products_lead_time = products_lead_time || 0;
    state.min_booking_duration = min_booking_duration || 240;
    state.normal_min_booking_duration = min_booking_duration || 240;
    state.long_weekend_data = long_weekend_data || null;
    state.subscription_min_booking_duration =
      home_product_tabs.find(
        (product) => product.type === "ZOOMCAR_SUBSCRIPTION"
      )?.min_duration || 10080;

    // handle ride lead time
    const leadTime = 24*60//state.products_lead_time?.normal?.lead_time;
    let minStarts = roundUpTime(new Date());
    let startDate = roundUpTime(new Date());
    minStarts.setMinutes(minStarts.getMinutes() + state.products_lead_time?.normal?.min_starts || 0)
    startDate.setMinutes(startDate.getMinutes() + leadTime);

    state.minStarts = startDate;
    state.starts =
      new Date(`${state.starts}`).getTime() > new Date(`${minStarts}`).getTime()
        ? state.starts
        : startDate;

        if ("subscription" in router.currentRoute.query || state.isSubscription) {
          const isSubscription = state.isSubscription || router.currentRoute.query.subscription == "true";
          const product = state.home_products.find(
            (product) =>
            product.type === (isSubscription ? "ZOOMCAR_SUBSCRIPTION" : "NORMAL")
          );
          if (product) {        
            if(product.type == "ZOOMCAR_SUBSCRIPTION") {
              state.isSubscription = true
              state.min_booking_duration = state.subscription_min_booking_duration;
            }
            else{
              state.isSubscription = false
              state.min_booking_duration = state.normal_min_booking_duration;
            }
            sessionStorage.setItem("product_type", product.type);
            
          }
        }

    let endDate = new Date(state.starts.getTime());
   endDate.setMinutes(state.starts.getMinutes() + 48 * 60); //(state.min_booking_duration));   
   if (!state.ends || state.ends < endDate) {
      state.ends = endDate;
    }
  },

  setMinDuration(state, duration) {
    state.min_booking_duration = duration;
  },

  updateOriginStarts: (state, { starts, min_booking_duration }) => {
    const minDuration = Math.max(
      48 * 60,
      min_booking_duration || state.min_booking_duration || 0
    );
    state.starts =
      new Date(`${state.starts}`).getTime() > new Date(`${starts}`).getTime()
        ? state.starts
        : starts;

    let endDate = new Date(state.starts.getTime());
    endDate.setMinutes(state.starts.getMinutes() + minDuration);
    if (!state.ends || state.ends < endDate) {
      state.ends = endDate;
    }
  },

  setDateTime: (state, payload) => {
    return (state[payload.point] = new Date(payload.date));
  },
  resetEndDateTime: (state) => {
    if (!state.starts) return;
    let endDate = new Date(state.starts.getTime());
    endDate.setMinutes(state.starts.getMinutes() + state.min_booking_duration);

    if (state.ends < endDate) state.ends = endDate;
  },

  setProduct: (state, product) => {
    state.product = product;
    if (product === "round_trip") {
      state.destination = {
        city: null,
        name: null,
        lat: null,
        lng: null,
      };
    }
  },

  setsuggested: (state, suggested) => {
    return (state.suggested = suggested);
  },

  setOrigin: (state, origin) => {
    state.origin = {
      ...state.origin,
      name: origin.name,
      lat: origin.lat,
      lng: origin.lng,
      setByLocality: origin.setByLocality || false,
      lastSetTimestamp: Date.now(),
    };
  },

  setOriginFromLocality: (state, origin) => {
    state.origin = {
      ...state.origin,
      name: origin.name,
      lat: origin.lat,
      lng: origin.lng,
      setByLocality: true,
      lastSetTimestamp: Date.now(),
    };
  },

  setOriginCity: (state, OriginCity) =>
    (state.origin = { ...state.origin, city: OriginCity }),

  setDestination: (state, destination) =>
    (state.destination = {
      ...state.destination,
      name: destination.name,
      lat: destination.lat,
      lng: destination.lng,
    }),

  setDestinationCity: (state, destinationCity) =>
    (state.destination = { ...state.destination, city: destinationCity }),

  setDistance: (state, distance) => (state.distance = distance),

  setDuration: (state, duration) => (state.duration = duration),

  setLatLng: (state, latLng) =>
    (state[latLng.point] = {
      ...state[latLng.point],
      lat: latLng.lat,
      lng: latLng.lng,
    }),

  setLocationName: (state, location) =>
    (state[location.point] = { ...state[location.point], name: location.name }),

  setGeoSessionToken: () => {
    const geo_session_token = randomstring.generate(16);
    state["geo_session_token"] = geo_session_token;
  },

  setPagination: (state, value) => {
    state.pagination = value;
  },
  setCityId: (state, value) => {
    state.city_id = value;
  },
  setFts: (state, value) => {
    state.fts = value;
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
