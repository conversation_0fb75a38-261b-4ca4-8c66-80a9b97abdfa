import schemaData from '~/data/default-schema.json'
import carModelCityData from '~/data/car-model-city.json'

export const sanitizeHtml = () => {

  const h1 = document.querySelector("#generic-h1")
  if (h1) {
    h1.remove()
  }

  const existingZoomcarSchema = document.querySelector('#zoomcar-schema')
  if (existingZoomcarSchema) {
    existingZoomcarSchema.remove()
  }

  const existingFaqSchema = document.querySelector('#generic-faq-schema')
  if (existingFaqSchema) {
    existingFaqSchema.remove()
  }

  const existingCarModelSchema = document.querySelector('#car-model-schema')
  if (existingCarModelSchema) {
    existingCarModelSchema.remove()
  }

  const existingItemsListSchema = document.querySelector('#items-list-schema')
  if (existingItemsListSchema) {
    existingItemsListSchema.remove()
  }

}

export const setLandingPageSchema = () => {
  const existingZoomcarSchema = document.querySelector('#zoomcar-schema')
  if (existingZoomcarSchema) {
    existingZoomcarSchema.remove()
  }

  const existingFaqSchema = document.querySelector('#generic-faq-schema')
  if (existingFaqSchema) {
    existingFaqSchema.remove()
  }
  
  const zoomcarSchema = schemaData['zoomcar-schema']
  const faqSchema = schemaData['generic-faq-schema']

  //zoomcar schema
  const script = document.createElement('script')
  script.id = 'zoomcar-schema'
  script.type = 'application/ld+json'
  script.text = JSON.stringify(zoomcarSchema)
  document.head.appendChild(script)

  //faq schema
  const faqScript = document.createElement('script')
  faqScript.id = 'generic-faq-schema'
  faqScript.type = 'application/ld+json'
  faqScript.text = JSON.stringify(faqSchema)
  document.head.appendChild(faqScript)
}

export const setCarModelRouteSchema = (to) => {
  const existingCarModelSchema = document.querySelector('#car-model-schema')
  if (existingCarModelSchema) {
    existingCarModelSchema.remove()
  }

  const { car_model: carModel, city_link } = to.params
  const city = city_link.charAt(0).toUpperCase() + city_link.slice(1)
  
  const carModelInfo = carModelCityData['car-models'].find(
    car => car['route-param'].toLowerCase() === carModel.toLowerCase()
  )

  if (!carModelInfo) return

  // Product Schema
  const productSchema = JSON.parse(JSON.stringify(carModelCityData['schema-product']))
  productSchema.name = productSchema.name
    .replace('{car-model}', carModelInfo['car-model'])
    .replace('{city}', city)
  productSchema.description = productSchema.description
    .replace('{car-model}', carModelInfo['car-model'])
    .replace('{city}', city)
  productSchema.brand.name = carModelInfo['brand']
  productSchema.image = carModelInfo['car-image-url']
  productSchema.offers.url = carModelInfo['car-url'] || window.location.href

  // FAQ Schema
  const faqSchema = JSON.parse(JSON.stringify(carModelCityData['schema-faq']))
  faqSchema.mainEntity = faqSchema.mainEntity.map(qa => ({
    ...qa,
    name: qa.name
      .replace('{car-model}', carModelInfo['car-model'])
      .replace('{city}', city),
    acceptedAnswer: {
      ...qa.acceptedAnswer,
      text: qa.acceptedAnswer.text
        .replace('{car-model}', carModelInfo['car-model'])
        .replace('{city}', city)
    }
  }))

  // Add structured data to page
  const script = document.createElement('script')
  script.id = 'car-model-schema'
  script.type = 'application/ld+json'
  script.text = JSON.stringify({
    '@context': 'https://schema.org',
    '@graph': [productSchema, faqSchema]
  })
  document.head.appendChild(script)
}


export const setItemListSchema = (items, defaultParams={}, defaultQuery={}) => {

  const existingItemsListSchema = document.querySelector('#items-list-schema')
  if (existingItemsListSchema) {
    existingItemsListSchema.remove()
  }

  const defaultItemSchema =  schemaData["default-item-schema"]

  let itemList = []
  for (let i = 0; i < 4; i++) {
    const nameSplit = items[i].name.split(' ')
    const model = nameSplit.slice(0, nameSplit.length - 1).join(' ')

    const routePath = `/${defaultParams.country_code}/${defaultParams.city_link}/cars/${items[i]["car_id"]}`;

    let fullPath;
    if (items[i].name) {
      const car_name_slug = items[i].name.toLowerCase().replace(/[\s_,]/g, "-");
      fullPath = `${routePath}/${car_name_slug}`;
    } else {
      fullPath = routePath;
    }
    
    const allQueryParams = {
      ...defaultQuery, // This includes starts, ends, lat, lng
      location_id: items[i].old_checkout_params.location_id,
      cargroup_id: items[i].cargroup_id,
    };
    
    const queryString = Object.keys(allQueryParams)
      .map(key => {
        return `${encodeURIComponent(key)}=${encodeURIComponent(allQueryParams[key])}`;
      })
      .join('&');
    
    const url = `${window.location.origin}${fullPath}${queryString ? `?${queryString}` : ''}`;

    const itemSchema = {
      ...defaultItemSchema,
      "position": i+1,
      "item": {
        ...defaultItemSchema.item,
        "name": items[i].name,
        "model": model,
        "fuelType": items[i].accessories[1],
        "vehicleTransmission": items[i].accessories[0],
        "image": items[i].images[0],
        "offers": {
          ...defaultItemSchema.item.offers,
          url: url,
          "price": items[i]["price_line_2"].raw,
        },
        "aggregateRating": {
          ...defaultItemSchema.item.aggregateRating,
          "ratingValue": parseFloat(items[i].rating.value),
          "reviewCount": parseInt(items[i].rating.trips)
        }
      }
    }

    itemList.push(itemSchema)
  }

  // Add structured data to page
  const script = document.createElement('script')
  script.id = 'items-list-schema'
  script.type = 'application/ld+json'
  script.text = JSON.stringify({
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": itemList
  })
  document.head.appendChild(script)

}